/**
 * Type definitions for the new streaming architecture
 * Supports tab-streamer and video-frame-interceptor components
 */

export interface StreamingConfig {
  frameRate: number;
  debug: boolean;
  timeout: number;
}

export interface Viewport {
  width: number;
  height: number;
}

export interface BoundingBox {
  x: number;
  y: number;
  width: number;
  height: number;
}

export interface FrameMetadata {
  timestamp: number;
  dimensions: Viewport;
  cropRegion?: BoundingBox;
  frameCount?: number;
}

export interface SubscriberOptions {
  throttle?: number; // ms between frames
  priority?: 'high' | 'normal' | 'low';
  enableCropping?: boolean;
}

export interface FrameSubscriber {
  id: string;
  callback: (frame: VideoFrame, metadata: FrameMetadata) => Promise<void> | void;
  options?: SubscriberOptions;
}

export interface ProcessedFrame {
  frame: VideoFrame;
  metadata: FrameMetadata;
  processed: boolean;
}

// WebRTC related types
export interface WebRTCConfig {
  iceServers?: RTCIceServer[];
  timeout?: number;
}

export interface StreamingState {
  isInitialized: boolean;
  isStreaming: boolean;
  hasActiveSubscribers: boolean;
  frameCount: number;
  lastFrameTime: number;
}

// Error types
export class TabStreamerError extends Error {
  constructor(
    message: string,
    public readonly code: string,
    public readonly source: 'initialization' | 'streaming' | 'webrtc' = 'streaming',
  ) {
    super(`[kazeel][tab-streamer] ${message}`);
    this.name = 'TabStreamerError';
  }
}

export class VideoFrameInterceptorError extends Error {
  constructor(
    message: string,
    public readonly code: string,
    public readonly source: 'processing' | 'subscription' = 'processing',
  ) {
    super(`[kazeel][video-frame-interceptor] ${message}`);
    this.name = 'VideoFrameInterceptorError';
  }
}

// Component interfaces
export interface TabStreamerInterface {
  // Core streaming functionality
  init(wsEndpoint: string, viewport: Viewport): Promise<void>;
  start(): Promise<MediaStream>;
  stop(): Promise<void>;
  
  // WebRTC management
  createPeerConnection(): Promise<RTCPeerConnection>;
  handleOffer(offer: RTCSessionDescriptionInit): Promise<void>;
  handleAnswer(answer: RTCSessionDescriptionInit): Promise<void>;
  handleIceCandidate(candidate: RTCIceCandidateInit): Promise<void>;
  
  // Stream management
  getStream(): MediaStream | null;
  isStreaming(): boolean;
  getState(): StreamingState;
}

export interface VideoFrameInterceptorInterface {
  // Frame processing
  initialize(videoTrack: MediaStreamTrack): MediaStreamTrack;
  setCropRegion(region: BoundingBox): void;
  enable(): void;
  disable(): void;
  
  // Subscription management
  subscribe(subscriber: FrameSubscriber): () => void;
  unsubscribe(id: string): void;
  
  // Frame analysis
  processFrame(frame: VideoFrame): Promise<ProcessedFrame>;
  getSubscriberCount(): number;
  isEnabled(): boolean;
}

// Legacy compatibility types
export interface LegacyScreenCropperInterface {
  init(wsEndpoint: string, viewport: Viewport): Promise<void>;
  start(viewport: Viewport): Promise<void>;
  stopStreaming(): Promise<void>;
  updateCropBox(cropBox: BoundingBox): Promise<void>;
  registerCaptchaDetectorCallback(callback: Function): void;
  startCapturingForCaptchaDetector(): void;
  stopCapturingForCaptchaDetector(): void;
  pauseFrameSending(): void;
  resumeFrameSending(): void;
}

// WebSocket message types for streaming
export interface StreamingWebSocketMessage {
  type: 'web-client-ready' | 'tab-streamer-offer' | 'interactivity-status' | 'stream-stopped';
  webClientId?: string;
  offer?: RTCSessionDescriptionInit;
  status?: 'paused' | 'enabled' | 'completed';
  data?: any;
}
