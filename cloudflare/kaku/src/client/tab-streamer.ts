/**
 * TabStreamer - WebRTC Streaming Component
 * 
 * Focused solely on WebRTC streaming functionality:
 * - Stream capture via getDisplayMedia()
 * - WebRTC peer connection management
 * - WebSocket signaling communication
 * - Connection lifecycle management
 */

import {
  TabStreamerInterface,
  TabStreamerError,
  Viewport,
  StreamingConfig,
  StreamingState,
  WebRTCConfig,
  StreamingWebSocketMessage,
} from './types/streaming';

(function () {
  const defaultConfig: StreamingConfig = {
    frameRate: 15,
    debug: true,
    timeout: 30000,
  };

  const defaultWebRTCConfig: WebRTCConfig = {
    iceServers: [{ urls: 'stun:stun.l.google.com:19302' }],
    timeout: 30000,
  };

  class TabStreamer implements TabStreamerInterface {
    private config: StreamingConfig;
    private webRTCConfig: WebRTCConfig;
    private socket: WebSocket | null = null;
    private peerConnection: RTCPeerConnection | null = null;
    private stream: MediaStream | null = null;
    private state: StreamingState = {
      isInitialized: false,
      isStreaming: false,
      hasActiveSubscribers: false,
      frameCount: 0,
      lastFrameTime: 0,
    };

    constructor(config?: Partial<StreamingConfig>, webRTCConfig?: Partial<WebRTCConfig>) {
      this.config = { ...defaultConfig, ...config };
      this.webRTCConfig = { ...defaultWebRTCConfig, ...webRTCConfig };
    }

    private log(...args: any[]): void {
      if (this.config.debug) {
        console.log('[kazeel][tab-streamer]', ...args);
      }
    }

    private error(...args: any[]): void {
      console.error('[kazeel][tab-streamer]', ...args);
    }

    /**
     * Initialize the tab streamer with WebSocket connection
     */
    async init(wsEndpoint: string, viewport: Viewport): Promise<void> {
      if (this.state.isInitialized) {
        this.log('Tab streamer already initialized');
        return;
      }

      try {
        this.log('Initializing tab streamer with endpoint:', wsEndpoint);
        
        // Initialize WebSocket connection
        await this.initializeWebSocket(wsEndpoint);
        
        this.state.isInitialized = true;
        this.log('Tab streamer initialized successfully');
      } catch (error) {
        const err = new TabStreamerError(
          `Failed to initialize: ${error}`,
          'INIT_FAILED',
          'initialization'
        );
        this.error(err.message, error);
        throw err;
      }
    }

    /**
     * Start streaming by capturing display media
     */
    async start(): Promise<MediaStream> {
      if (!this.state.isInitialized) {
        throw new TabStreamerError(
          'Tab streamer not initialized. Call init() first.',
          'NOT_INITIALIZED',
          'streaming'
        );
      }

      if (this.state.isStreaming) {
        this.log('Already streaming, returning existing stream');
        return this.stream!;
      }

      try {
        this.log('Starting tab streaming...');
        
        // Capture display media
        this.stream = await this.captureDisplayMedia();
        
        // Create peer connection
        this.peerConnection = await this.createPeerConnection();
        
        // Add tracks to peer connection
        this.stream.getTracks().forEach((track) => {
          this.peerConnection!.addTrack(track, this.stream!);
        });

        this.state.isStreaming = true;
        this.log('Tab streaming started successfully');
        
        return this.stream;
      } catch (error) {
        const err = new TabStreamerError(
          `Failed to start streaming: ${error}`,
          'START_FAILED',
          'streaming'
        );
        this.error(err.message, error);
        throw err;
      }
    }

    /**
     * Stop streaming and cleanup resources
     */
    async stop(): Promise<void> {
      this.log('Stopping tab streaming...');

      try {
        // Stop all tracks
        if (this.stream) {
          this.stream.getTracks().forEach(track => track.stop());
          this.stream = null;
        }

        // Close peer connection
        if (this.peerConnection) {
          this.peerConnection.close();
          this.peerConnection = null;
        }

        // Close WebSocket
        if (this.socket) {
          this.socket.close();
          this.socket = null;
        }

        this.state.isStreaming = false;
        this.log('Tab streaming stopped successfully');
      } catch (error) {
        const err = new TabStreamerError(
          `Failed to stop streaming: ${error}`,
          'STOP_FAILED',
          'streaming'
        );
        this.error(err.message, error);
        throw err;
      }
    }

    /**
     * Create WebRTC peer connection
     */
    async createPeerConnection(): Promise<RTCPeerConnection> {
      this.log('Creating WebRTC peer connection...');

      const pc = new RTCPeerConnection(this.webRTCConfig);

      // Set up event handlers
      pc.onicecandidate = (event) => {
        if (event.candidate && this.socket) {
          this.sendWebSocketMessage({
            type: 'tab-streamer-candidate',
            candidate: event.candidate,
          });
        }
      };

      pc.onconnectionstatechange = () => {
        this.log('Peer connection state:', pc.connectionState);
      };

      pc.oniceconnectionstatechange = () => {
        this.log('ICE connection state:', pc.iceConnectionState);
      };

      return pc;
    }

    /**
     * Handle WebRTC offer from web client
     */
    async handleOffer(offer: RTCSessionDescriptionInit): Promise<void> {
      if (!this.peerConnection) {
        throw new TabStreamerError(
          'No peer connection available',
          'NO_PEER_CONNECTION',
          'webrtc'
        );
      }

      try {
        await this.peerConnection.setRemoteDescription(offer);
        const answer = await this.peerConnection.createAnswer();
        await this.peerConnection.setLocalDescription(answer);

        this.sendWebSocketMessage({
          type: 'tab-streamer-answer',
          answer: answer,
        });

        this.log('WebRTC offer handled successfully');
      } catch (error) {
        throw new TabStreamerError(
          `Failed to handle offer: ${error}`,
          'OFFER_FAILED',
          'webrtc'
        );
      }
    }

    /**
     * Handle WebRTC answer from web client
     */
    async handleAnswer(answer: RTCSessionDescriptionInit): Promise<void> {
      if (!this.peerConnection) {
        throw new TabStreamerError(
          'No peer connection available',
          'NO_PEER_CONNECTION',
          'webrtc'
        );
      }

      try {
        await this.peerConnection.setRemoteDescription(answer);
        this.log('WebRTC answer handled successfully');
      } catch (error) {
        throw new TabStreamerError(
          `Failed to handle answer: ${error}`,
          'ANSWER_FAILED',
          'webrtc'
        );
      }
    }

    /**
     * Handle ICE candidate from web client
     */
    async handleIceCandidate(candidate: RTCIceCandidateInit): Promise<void> {
      if (!this.peerConnection) {
        throw new TabStreamerError(
          'No peer connection available',
          'NO_PEER_CONNECTION',
          'webrtc'
        );
      }

      try {
        await this.peerConnection.addIceCandidate(candidate);
        this.log('ICE candidate handled successfully');
      } catch (error) {
        throw new TabStreamerError(
          `Failed to handle ICE candidate: ${error}`,
          'ICE_CANDIDATE_FAILED',
          'webrtc'
        );
      }
    }

    /**
     * Get current stream
     */
    getStream(): MediaStream | null {
      return this.stream;
    }

    /**
     * Check if currently streaming
     */
    isStreaming(): boolean {
      return this.state.isStreaming;
    }

    /**
     * Get current state
     */
    getState(): StreamingState {
      return { ...this.state };
    }

    /**
     * Capture display media with current tab preference
     */
    private async captureDisplayMedia(): Promise<MediaStream> {
      this.log('Requesting display media stream...');

      try {
        const stream = await navigator.mediaDevices.getDisplayMedia({
          video: {
            frameRate: this.config.frameRate,
            width: { ideal: 1280 },
            height: { ideal: 720 },
          },
          audio: false,
          // @ts-ignore - preferCurrentTab is not in standard types but supported by Chrome
          preferCurrentTab: true,
        });

        this.log('Display media stream obtained successfully');
        return stream;
      } catch (error) {
        throw new TabStreamerError(
          `Failed to get display media stream: ${error}`,
          'DISPLAY_MEDIA_FAILED',
          'streaming'
        );
      }
    }

    /**
     * Initialize WebSocket connection
     */
    private async initializeWebSocket(wsEndpoint: string): Promise<void> {
      return new Promise((resolve, reject) => {
        try {
          this.socket = new WebSocket(wsEndpoint);

          this.socket.onopen = () => {
            this.log('WebSocket connection established');
            resolve();
          };

          this.socket.onmessage = (event) => {
            this.handleWebSocketMessage(JSON.parse(event.data));
          };

          this.socket.onerror = (error) => {
            this.error('WebSocket error:', error);
            reject(new TabStreamerError(
              'WebSocket connection failed',
              'WEBSOCKET_FAILED',
              'initialization'
            ));
          };

          this.socket.onclose = () => {
            this.log('WebSocket connection closed');
          };

          // Timeout handling
          setTimeout(() => {
            if (this.socket?.readyState !== WebSocket.OPEN) {
              reject(new TabStreamerError(
                'WebSocket connection timeout',
                'WEBSOCKET_TIMEOUT',
                'initialization'
              ));
            }
          }, this.config.timeout);

        } catch (error) {
          reject(new TabStreamerError(
            `Failed to create WebSocket: ${error}`,
            'WEBSOCKET_CREATE_FAILED',
            'initialization'
          ));
        }
      });
    }

    /**
     * Handle incoming WebSocket messages
     */
    private handleWebSocketMessage(message: StreamingWebSocketMessage): void {
      this.log('Received WebSocket message:', message.type);

      switch (message.type) {
        case 'web-client-ready':
          this.handleWebClientReady(message);
          break;
        case 'interactivity-status':
          this.handleInteractivityStatus(message);
          break;
        default:
          this.log('Unknown message type:', message.type);
      }
    }

    /**
     * Handle web client ready message
     */
    private async handleWebClientReady(message: StreamingWebSocketMessage): Promise<void> {
      try {
        if (!this.peerConnection) {
          this.peerConnection = await this.createPeerConnection();
        }

        const offer = await this.peerConnection.createOffer();
        await this.peerConnection.setLocalDescription(offer);

        this.sendWebSocketMessage({
          type: 'tab-streamer-offer',
          offer: offer,
          webClientId: message.webClientId,
        });

        this.log('✅ [WebRTC] Sent tab-streamer-offer with webClientId:', message.webClientId);
      } catch (error) {
        this.error('Failed to handle web client ready:', error);
      }
    }

    /**
     * Handle interactivity status changes
     */
    private handleInteractivityStatus(message: StreamingWebSocketMessage): void {
      this.log('Interactivity status:', message.status);

      switch (message.status) {
        case 'paused':
          // Pause streaming if needed
          break;
        case 'enabled':
          // Resume streaming if needed
          break;
        case 'completed':
          this.log('Captcha solved, stopping streaming');
          this.stop();
          break;
      }
    }

    /**
     * Send message via WebSocket
     */
    private sendWebSocketMessage(message: any): void {
      if (this.socket && this.socket.readyState === WebSocket.OPEN) {
        this.socket.send(JSON.stringify(message));
      } else {
        this.error('Cannot send message: WebSocket not connected');
      }
    }
  }

  // Create global instance
  const tabStreamer = new TabStreamer();

  // Expose to global scope
  (globalThis as any).tabStreamer = tabStreamer;

  console.log('[kazeel][tab-streamer] Tab streamer script loaded');
})();
