/**
 * VideoFrameInterceptor - Frame Processing Component
 *
 * Focused solely on frame processing and analysis:
 * - Frame transformation and cropping
 * - Subscription management for frame analysis
 * - Performance optimization for frame processing
 * - Integration with captcha detection
 */

import {
  VideoFrameInterceptorInterface,
  VideoFrameInterceptorError,
  BoundingBox,
  FrameSubscriber,
  FrameMetadata,
  ProcessedFrame,
  SubscriberOptions,
} from './types/streaming';

(function () {
  const config = {
    debug: true,
    maxSubscribers: 10,
    defaultThrottle: 100, // ms
  };

  class VideoFrameInterceptor implements VideoFrameInterceptorInterface {
    private originalTrack: MediaStreamTrack | null = null;
    private processor: MediaStreamTrackProcessor | null = null;
    private generator: MediaStreamTrackGenerator | null = null;
    private transformStream: TransformStream | null = null;
    private subscribers = new Map<string, FrameSubscriber>();
    private cropRegion: BoundingBox | null = null;
    private enabled = true;
    private frameCount = 0;
    private lastFrameTime = 0;

    private log(...args: any[]): void {
      if (config.debug) {
        console.log('[kazeel][video-frame-interceptor]', ...args);
      }
    }

    private error(...args: any[]): void {
      console.error('[kazeel][video-frame-interceptor]', ...args);
    }

    /**
     * Initialize the interceptor with a video track
     */
    initialize(videoTrack: MediaStreamTrack): MediaStreamTrack {
      if (!videoTrack || videoTrack.kind !== 'video') {
        throw new VideoFrameInterceptorError(
          'VideoFrameInterceptor requires a valid video track',
          'INVALID_TRACK',
          'processing',
        );
      }

      this.originalTrack = videoTrack;
      this.log('Initializing interceptor with video track:', videoTrack.label);

      try {
        // Create processor and generator
        this.processor = new MediaStreamTrackProcessor({ track: videoTrack });
        this.generator = new MediaStreamTrackGenerator({ kind: 'video' });

        // Create transform stream
        this.transformStream = new TransformStream({
          transform: this.transformFrame.bind(this),
        });

        // Connect the pipeline
        this.processor.readable
          .pipeThrough(this.transformStream)
          .pipeTo(this.generator.writable)
          .catch((error) => {
            this.error('Pipeline error:', error);
          });

        this.log('Video frame interceptor initialized successfully');
        return this.generator;
      } catch (error) {
        throw new VideoFrameInterceptorError(
          `Failed to initialize interceptor: ${error}`,
          'INIT_FAILED',
          'processing',
        );
      }
    }

    /**
     * Set crop region for frame processing
     */
    setCropRegion(region: BoundingBox): void {
      this.cropRegion = { ...region };
      this.log('Crop region updated:', this.cropRegion);
    }

    /**
     * Enable frame processing
     */
    enable(): void {
      this.enabled = true;
      this.log('Frame interceptor enabled');
    }

    /**
     * Disable frame processing
     */
    disable(): void {
      this.enabled = false;
      this.log('Frame interceptor disabled');
    }

    /**
     * Subscribe to frame processing
     */
    subscribe(subscriber: FrameSubscriber): () => void {
      if (this.subscribers.size >= config.maxSubscribers) {
        throw new VideoFrameInterceptorError(
          `Maximum subscribers limit reached (${config.maxSubscribers})`,
          'MAX_SUBSCRIBERS',
          'subscription',
        );
      }

      if (this.subscribers.has(subscriber.id)) {
        this.log('Subscriber already exists, updating:', subscriber.id);
      }

      // Apply default options
      const options: SubscriberOptions = {
        throttle: config.defaultThrottle,
        priority: 'normal',
        enableCropping: true,
        ...subscriber.options,
      };

      this.subscribers.set(subscriber.id, {
        ...subscriber,
        options,
      });

      this.log('Subscriber added:', subscriber.id, 'Total subscribers:', this.subscribers.size);

      // Return unsubscribe function
      return () => this.unsubscribe(subscriber.id);
    }

    /**
     * Unsubscribe from frame processing
     */
    unsubscribe(id: string): void {
      if (this.subscribers.delete(id)) {
        this.log('Subscriber removed:', id, 'Remaining subscribers:', this.subscribers.size);
      } else {
        this.log('Subscriber not found:', id);
      }
    }

    /**
     * Process a single frame
     */
    async processFrame(frame: VideoFrame): Promise<ProcessedFrame> {
      const metadata: FrameMetadata = {
        timestamp: frame.timestamp || performance.now(),
        dimensions: {
          width: frame.codedWidth,
          height: frame.codedHeight,
        },
        cropRegion: this.cropRegion || undefined,
        frameCount: this.frameCount,
      };

      return {
        frame,
        metadata,
        processed: true,
      };
    }

    /**
     * Get number of active subscribers
     */
    getSubscriberCount(): number {
      return this.subscribers.size;
    }

    /**
     * Check if interceptor is enabled
     */
    isEnabled(): boolean {
      return this.enabled;
    }

    /**
     * Transform function for processing individual frames
     */
    private async transformFrame(
      frame: VideoFrame,
      controller: TransformStreamDefaultController,
    ): Promise<void> {
      try {
        this.frameCount++;
        const currentTime = performance.now();

        // Throttle debug logging
        if (config.debug && currentTime - this.lastFrameTime > 1000) {
          this.log(`Processed ${this.frameCount} frames`);
          this.lastFrameTime = currentTime;
        }

        let processedFrame = frame;

        // Apply cropping if enabled and crop region is set
        if (this.enabled && this.cropRegion) {
          processedFrame = this.applyCropping(frame);
        }

        // Notify subscribers with the processed frame
        if (this.subscribers.size > 0) {
          await this.notifySubscribers(processedFrame);
        }

        // Send frame to the output stream (to WebRTC peer connection)
        if (this.enabled) {
          controller.enqueue(processedFrame);
        } else {
          // If interceptor is disabled, pass through original frame
          controller.enqueue(frame);
        }

        // Clean up the original frame if we created a new one
        if (processedFrame !== frame) {
          frame.close();
        }
      } catch (error) {
        this.error('Error processing frame:', error);
        // Pass through original frame on error
        controller.enqueue(frame);
      }
    }

    /**
     * Apply cropping to a video frame
     */
    private applyCropping(frame: VideoFrame): VideoFrame {
      if (!this.cropRegion) {
        return frame;
      }

      const { codedWidth, codedHeight } = frame;

      // Ensure crop region is within frame bounds
      const safeCropRegion = {
        x: Math.max(0, Math.min(this.cropRegion.x, codedWidth)),
        y: Math.max(0, Math.min(this.cropRegion.y, codedHeight)),
        width: Math.min(this.cropRegion.width, codedWidth - this.cropRegion.x),
        height: Math.min(this.cropRegion.height, codedHeight - this.cropRegion.y),
      };

      // Ensure even dimensions for YUV 4:2:0 alignment
      safeCropRegion.x = this.makeEven(safeCropRegion.x);
      safeCropRegion.y = this.makeEven(safeCropRegion.y);
      safeCropRegion.width = this.makeEven(safeCropRegion.width);
      safeCropRegion.height = this.makeEven(safeCropRegion.height);

      try {
        return new VideoFrame(frame, {
          visibleRect: safeCropRegion,
          displayWidth: safeCropRegion.width,
          displayHeight: safeCropRegion.height,
          timestamp: frame.timestamp,
          duration: frame.duration,
        });
      } catch (error) {
        this.error('Failed to crop frame:', error, 'safeCropRegion:', safeCropRegion);
        return frame; // Return original frame on error
      }
    }

    /**
     * Notify all subscribers about a new frame
     */
    private async notifySubscribers(frame: VideoFrame): Promise<void> {
      const metadata: FrameMetadata = {
        timestamp: frame.timestamp || performance.now(),
        dimensions: {
          width: frame.codedWidth,
          height: frame.codedHeight,
        },
        cropRegion: this.cropRegion || undefined,
        frameCount: this.frameCount,
      };

      const notifications = Array.from(this.subscribers.values()).map(async (subscriber) => {
        try {
          // Apply throttling if specified
          if (subscriber.options?.throttle) {
            const now = performance.now();
            const lastNotified = (subscriber as any).lastNotified || 0;
            if (now - lastNotified < subscriber.options.throttle) {
              return; // Skip this frame due to throttling
            }
            (subscriber as any).lastNotified = now;
          }

          await subscriber.callback(frame, metadata);
        } catch (error) {
          this.error(`Error in subscriber ${subscriber.id}:`, error);
        }
      });

      await Promise.allSettled(notifications);
    }

    /**
     * Ensure number is even (required for YUV 4:2:0 alignment)
     */
    private makeEven(value: number): number {
      return Math.floor(value / 2) * 2;
    }

    /**
     * Cleanup resources
     */
    destroy(): void {
      this.log('Destroying video frame interceptor...');

      // Clear all subscribers
      this.subscribers.clear();

      // Stop original track if we own it
      if (this.originalTrack) {
        this.originalTrack.stop();
        this.originalTrack = null;
      }

      // Clean up processor and generator
      this.processor = null;
      this.generator = null;
      this.transformStream = null;

      this.log('Video frame interceptor destroyed');
    }
  }

  // Create global instance
  const videoFrameInterceptor = new VideoFrameInterceptor();

  // Expose to global scope
  (globalThis as any).videoFrameInterceptor = videoFrameInterceptor;

  console.log('[kazeel][video-frame-interceptor] Video frame interceptor script loaded');
})();
