<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Video Frame Interceptor Demo (Control Tab Processing)</title>
    <style>
      body {
        font-family: Arial, sans-serif;
        margin: 20px;
        background: #f0f0f0;
      }
      .container {
        max-width: 1200px;
        margin: 0 auto;
      }
      .section {
        background: white;
        padding: 20px;
        margin: 20px 0;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }
      .controls {
        display: flex;
        gap: 10px;
        margin: 10px 0;
        flex-wrap: wrap;
      }
      button {
        padding: 8px 16px;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-size: 14px;
      }
      .btn-primary {
        background: #007bff;
        color: white;
      }
      .btn-success {
        background: #28a745;
        color: white;
      }
      .btn-warning {
        background: #ffc107;
        color: black;
      }
      .btn-danger {
        background: #dc3545;
        color: white;
      }
      .btn-secondary {
        background: #6c757d;
        color: white;
      }

      input[type="text"] {
        padding: 6px 10px;
        border: 1px solid #ddd;
        border-radius: 4px;
        font-size: 14px;
      }

      .status {
        background: #f8f9fa;
        padding: 10px;
        border-radius: 4px;
        margin: 10px 0;
        font-family: monospace;
        font-size: 12px;
      }

      .video-container {
        display: flex;
        gap: 20px;
        flex-wrap: wrap;
      }

      .video-box {
        flex: 1;
        min-width: 300px;
      }

      video {
        width: 100%;
        max-width: 400px;
        height: auto;
        background: #000;
        border-radius: 4px;
      }

      .stats {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 10px;
        margin: 10px 0;
      }

      .stat-item {
        background: #e9ecef;
        padding: 10px;
        border-radius: 4px;
        text-align: center;
      }

      .stat-value {
        font-size: 24px;
        font-weight: bold;
        color: #007bff;
      }

      .stat-label {
        font-size: 12px;
        color: #6c757d;
      }

      .log {
        background: #000;
        color: #00ff00;
        padding: 10px;
        border-radius: 4px;
        height: 200px;
        overflow-y: auto;
        font-family: monospace;
        font-size: 12px;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <h1>🎬 Video Frame Interceptor Demo</h1>
      <p>
        This demo shows how to use the VideoFrameInterceptor for real-time video
        processing and frame analysis. Note: In the actual tab streaming system,
        the interceptor runs in the control tab and processes incoming WebRTC
        streams.
      </p>

      <div class="section">
        <h2>📹 Video Capture & Processing</h2>
        <div class="controls">
          <button id="startCapture" class="btn-primary">Start Capture</button>
          <button id="stopCapture" class="btn-danger" disabled>
            Stop Capture
          </button>
          <button id="toggleInterceptor" class="btn-warning">
            Toggle Interceptor
          </button>
          <button id="toggleCropping" class="btn-secondary">
            Toggle Cropping
          </button>
        </div>

        <div class="video-container">
          <div class="video-box">
            <h3>Original Stream</h3>
            <video id="originalVideo" autoplay muted playsinline></video>
          </div>
          <div class="video-box">
            <h3>Processed Stream</h3>
            <video id="processedVideo" autoplay muted playsinline></video>
          </div>
        </div>
      </div>

      <div class="section">
        <h2>⚙️ Interceptor Controls</h2>
        <div class="controls">
          <input
            type="text"
            id="cropInput"
            placeholder="x,y,width,height (e.g., 100,100,400,300)"
          />
          <button id="setCrop" class="btn-success">Set Crop Region</button>
          <button id="clearCrop" class="btn-secondary">Clear Crop</button>
        </div>

        <div class="status" id="interceptorStatus">
          Interceptor Status: Not initialized
        </div>
      </div>

      <div class="section">
        <h2>📊 Screen Comparison Analysis</h2>
        <div class="controls">
          <button id="startAnalysis" class="btn-success" disabled>
            Start Analysis
          </button>
          <button id="stopAnalysis" class="btn-danger" disabled>
            Stop Analysis
          </button>
          <input
            type="number"
            id="thresholdInput"
            placeholder="Threshold (0.01-1.0)"
            step="0.01"
            min="0.01"
            max="1.0"
            value="0.1"
          />
          <button id="setThreshold" class="btn-primary">Set Threshold</button>
        </div>

        <div class="stats">
          <div class="stat-item">
            <div class="stat-value" id="frameCount">0</div>
            <div class="stat-label">Frames Processed</div>
          </div>
          <div class="stat-item">
            <div class="stat-value" id="changeCount">0</div>
            <div class="stat-label">Significant Changes</div>
          </div>
          <div class="stat-item">
            <div class="stat-value" id="changeRate">0%</div>
            <div class="stat-label">Change Rate</div>
          </div>
          <div class="stat-item">
            <div class="stat-value" id="subscribers">0</div>
            <div class="stat-label">Active Subscribers</div>
          </div>
        </div>
      </div>

      <div class="section">
        <h2>📝 Activity Log</h2>
        <div class="log" id="activityLog"></div>
        <div class="controls">
          <button id="clearLog" class="btn-secondary">Clear Log</button>
        </div>
      </div>
    </div>

    <!-- Include the required scripts -->
    <script src="../injection-scripts/video-frame-interceptor.js"></script>
    <script src="screen-comparison.js"></script>

    <script>
      // Demo application
      class InterceptorDemo {
        constructor() {
          this.originalStream = null;
          this.interceptor = null;
          this.screenComparison = null;
          this.isCapturing = false;

          this.setupEventListeners();
          this.log("Demo initialized");
        }

        setupEventListeners() {
          // Video capture controls
          document
            .getElementById("startCapture")
            .addEventListener("click", () => this.startCapture());
          document
            .getElementById("stopCapture")
            .addEventListener("click", () => this.stopCapture());
          document
            .getElementById("toggleInterceptor")
            .addEventListener("click", () => this.toggleInterceptor());
          document
            .getElementById("toggleCropping")
            .addEventListener("click", () => this.toggleCropping());

          // Crop controls
          document
            .getElementById("setCrop")
            .addEventListener("click", () => this.setCropRegion());
          document
            .getElementById("clearCrop")
            .addEventListener("click", () => this.clearCropRegion());

          // Analysis controls
          document
            .getElementById("startAnalysis")
            .addEventListener("click", () => this.startAnalysis());
          document
            .getElementById("stopAnalysis")
            .addEventListener("click", () => this.stopAnalysis());
          document
            .getElementById("setThreshold")
            .addEventListener("click", () => this.setThreshold());

          // Utility controls
          document
            .getElementById("clearLog")
            .addEventListener("click", () => this.clearLog());

          // Listen for screen changes
          window.addEventListener("screenchange", (event) => {
            this.log(
              `Screen change detected: ${(
                event.detail.difference * 100
              ).toFixed(2)}% difference`
            );
            this.updateStats();
          });
        }

        async startCapture() {
          try {
            this.log("Starting video capture...");

            // Get display media
            this.originalStream = await navigator.mediaDevices.getDisplayMedia({
              video: { width: 1280, height: 720, frameRate: 30 },
              audio: false,
            });

            // Show original video
            document.getElementById("originalVideo").srcObject =
              this.originalStream;

            // Initialize interceptor
            const videoTrack = this.originalStream.getVideoTracks()[0];
            this.interceptor = new VideoFrameInterceptor({
              debug: true,
              enableCropping: true,
            });

            // Process the video track
            const processedTrack = this.interceptor.initialize(videoTrack);
            const processedStream = new MediaStream([processedTrack]);

            // Show processed video
            document.getElementById("processedVideo").srcObject =
              processedStream;

            this.isCapturing = true;
            this.updateUI();
            this.updateStatus();
            this.log("Video capture started successfully");
          } catch (error) {
            this.log(`Error starting capture: ${error.message}`);
          }
        }

        stopCapture() {
          this.log("Stopping video capture...");

          if (this.screenComparison) {
            this.screenComparison.stop();
            this.screenComparison = null;
          }

          if (this.interceptor) {
            this.interceptor.destroy();
            this.interceptor = null;
          }

          if (this.originalStream) {
            this.originalStream.getTracks().forEach((track) => track.stop());
            this.originalStream = null;
          }

          document.getElementById("originalVideo").srcObject = null;
          document.getElementById("processedVideo").srcObject = null;

          this.isCapturing = false;
          this.updateUI();
          this.updateStatus();
          this.log("Video capture stopped");
        }

        toggleInterceptor() {
          if (!this.interceptor) return;

          const status = this.interceptor.getStatus();
          this.interceptor.setEnabled(!status.isEnabled);
          this.updateStatus();
          this.log(`Interceptor ${status.isEnabled ? "disabled" : "enabled"}`);
        }

        toggleCropping() {
          if (!this.interceptor) return;

          const status = this.interceptor.getStatus();
          this.interceptor.setCroppingEnabled(!status.croppingEnabled);
          this.updateStatus();
          this.log(
            `Cropping ${status.croppingEnabled ? "disabled" : "enabled"}`
          );
        }

        setCropRegion() {
          if (!this.interceptor) return;

          const input = document.getElementById("cropInput").value.trim();
          if (!input) return;

          const parts = input.split(",").map((p) => parseInt(p.trim()));
          if (parts.length !== 4 || parts.some(isNaN)) {
            this.log("Invalid crop format. Use: x,y,width,height");
            return;
          }

          const [x, y, width, height] = parts;
          this.interceptor.setCropRegion({ x, y, width, height });
          this.updateStatus();
          this.log(`Crop region set: ${x},${y} ${width}x${height}`);
        }

        clearCropRegion() {
          if (!this.interceptor) return;

          this.interceptor.setCropRegion(null);
          this.updateStatus();
          this.log("Crop region cleared");
        }

        startAnalysis() {
          if (!this.interceptor) return;

          const threshold =
            parseFloat(document.getElementById("thresholdInput").value) || 0.1;

          this.screenComparison = new ScreenComparison({
            debug: true,
            diffThreshold: threshold,
            analysisInterval: 500,
          });

          this.screenComparison.start(this.interceptor);
          this.updateUI();
          this.log(
            `Screen analysis started with ${(threshold * 100).toFixed(
              1
            )}% threshold`
          );
        }

        stopAnalysis() {
          if (this.screenComparison) {
            this.screenComparison.stop();
            this.screenComparison = null;
            this.updateUI();
            this.log("Screen analysis stopped");
          }
        }

        setThreshold() {
          if (!this.screenComparison) return;

          const threshold =
            parseFloat(document.getElementById("thresholdInput").value) || 0.1;
          this.screenComparison.options.diffThreshold = threshold;
          this.log(
            `Analysis threshold set to ${(threshold * 100).toFixed(1)}%`
          );
        }

        updateUI() {
          document.getElementById("startCapture").disabled = this.isCapturing;
          document.getElementById("stopCapture").disabled = !this.isCapturing;
          document.getElementById("startAnalysis").disabled =
            !this.interceptor || !!this.screenComparison;
          document.getElementById("stopAnalysis").disabled =
            !this.screenComparison;
        }

        updateStatus() {
          if (!this.interceptor) {
            document.getElementById("interceptorStatus").textContent =
              "Interceptor Status: Not initialized";
            return;
          }

          const status = this.interceptor.getStatus();
          document.getElementById("interceptorStatus").innerHTML = `
                    Interceptor Status: ${
                      status.isEnabled ? "Enabled" : "Disabled"
                    }<br>
                    Cropping: ${
                      status.croppingEnabled ? "Enabled" : "Disabled"
                    }<br>
                    Crop Region: ${
                      status.cropRegion
                        ? `${status.cropRegion.x},${status.cropRegion.y} ${status.cropRegion.width}x${status.cropRegion.height}`
                        : "None"
                    }<br>
                    Subscribers: ${status.subscriberCount}<br>
                    Frames Processed: ${status.frameCount}
                `;

          this.updateStats();
        }

        updateStats() {
          if (this.interceptor) {
            const status = this.interceptor.getStatus();
            document.getElementById("subscribers").textContent =
              status.subscriberCount;
          }

          if (this.screenComparison) {
            const stats = this.screenComparison.getStats();
            document.getElementById("frameCount").textContent =
              stats.frameCount;
            document.getElementById("changeCount").textContent =
              stats.significantChanges;
            document.getElementById("changeRate").textContent =
              (stats.changeRate * 100).toFixed(1) + "%";
          }
        }

        clearLog() {
          document.getElementById("activityLog").innerHTML = "";
        }

        log(message) {
          const timestamp = new Date().toLocaleTimeString();
          const logElement = document.getElementById("activityLog");
          logElement.innerHTML += `[${timestamp}] ${message}\n`;
          logElement.scrollTop = logElement.scrollHeight;
          console.log(`[Demo] ${message}`);
        }
      }

      // Initialize demo when page loads
      window.addEventListener("load", () => {
        window.demo = new InterceptorDemo();
      });
    </script>
  </body>
</html>
