import { POCStreamingSystem } from "./streaming-system.js";

const system = new POCStreamingSystem({
  signalingPort: 8081,
  webServerPort: 3001,
  browserPort: 9222,
});

try {
  await system.start();
  console.log("✅ System started successfully!");
  console.log("📋 Open multiple web clients at: http://localhost:3001");
  console.log(
    "🔧 Control tab should handle multiple clients with individual peer connections"
  );

  // Keep the process running
  process.on("SIGINT", async () => {
    console.log("\n🛑 Shutting down...");
    await system.stop();
    process.exit(0);
  });
} catch (error) {
  console.error("❌ Failed to start system:", error);
  process.exit(1);
}
