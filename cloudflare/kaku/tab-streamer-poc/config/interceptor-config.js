/**
 * Video Frame Interceptor Configuration
 * 
 * Centralized configuration for the VideoFrameInterceptor system.
 * Provides default settings, validation, and environment-specific configurations.
 */

// Default interceptor configuration
export const DEFAULT_INTERCEPTOR_CONFIG = {
  // Core functionality
  debug: false,
  enableCropping: true,
  
  // Performance settings
  frameRate: 30,
  maxSubscribers: 10,
  
  // Default crop region (null = no cropping)
  defaultCropRegion: null,
  
  // Analysis settings
  analysisInterval: 1000, // ms between frame analysis
  maxFrameHistory: 5,
  
  // Error handling
  maxRetries: 3,
  fallbackToOriginal: true,
  
  // Memory management
  autoCleanup: true,
  cleanupInterval: 30000, // 30 seconds
};

// Environment-specific configurations
export const ENVIRONMENT_CONFIGS = {
  development: {
    debug: true,
    frameRate: 15, // Lower frame rate for development
    analysisInterval: 500,
    maxFrameHistory: 10,
  },
  
  production: {
    debug: false,
    frameRate: 30,
    analysisInterval: 1000,
    maxFrameHistory: 5,
  },
  
  testing: {
    debug: true,
    frameRate: 10,
    analysisInterval: 100,
    maxFrameHistory: 3,
    maxSubscribers: 5,
  }
};

// Preset crop regions for common use cases
export const CROP_PRESETS = {
  // Common aspect ratios
  '16:9': { x: 0, y: 0, width: 1280, height: 720 },
  '4:3': { x: 0, y: 0, width: 1024, height: 768 },
  '1:1': { x: 0, y: 0, width: 720, height: 720 },
  
  // Common screen regions
  topHalf: { x: 0, y: 0, width: 1280, height: 360 },
  bottomHalf: { x: 0, y: 360, width: 1280, height: 360 },
  leftHalf: { x: 0, y: 0, width: 640, height: 720 },
  rightHalf: { x: 640, y: 0, width: 640, height: 720 },
  
  // Center regions
  centerQuarter: { x: 320, y: 180, width: 640, height: 360 },
  centerHalf: { x: 160, y: 90, width: 960, height: 540 },
  
  // Browser-specific regions
  browserContent: { x: 0, y: 80, width: 1280, height: 640 }, // Excluding browser UI
  mobileViewport: { x: 200, y: 100, width: 375, height: 667 }, // iPhone viewport simulation
};

// Analysis thresholds for different use cases
export const ANALYSIS_THRESHOLDS = {
  // Change detection sensitivity
  veryLow: 0.01,    // 1% change
  low: 0.05,        // 5% change
  medium: 0.1,      // 10% change
  high: 0.2,        // 20% change
  veryHigh: 0.5,    // 50% change
  
  // Specific use cases
  textChanges: 0.02,      // Sensitive to text updates
  imageChanges: 0.15,     // Less sensitive for image content
  videoChanges: 0.3,      // Even less sensitive for video content
  uiChanges: 0.05,        // Moderate sensitivity for UI updates
};

// Performance profiles
export const PERFORMANCE_PROFILES = {
  lowEnd: {
    frameRate: 15,
    analysisInterval: 2000,
    maxSubscribers: 3,
    maxFrameHistory: 2,
  },
  
  standard: {
    frameRate: 30,
    analysisInterval: 1000,
    maxSubscribers: 5,
    maxFrameHistory: 5,
  },
  
  highEnd: {
    frameRate: 60,
    analysisInterval: 500,
    maxSubscribers: 10,
    maxFrameHistory: 10,
  },
  
  realtime: {
    frameRate: 60,
    analysisInterval: 100,
    maxSubscribers: 15,
    maxFrameHistory: 15,
  }
};

/**
 * Configuration builder class
 */
export class InterceptorConfigBuilder {
  constructor() {
    this.config = { ...DEFAULT_INTERCEPTOR_CONFIG };
  }
  
  /**
   * Set environment-specific configuration
   */
  environment(env) {
    if (ENVIRONMENT_CONFIGS[env]) {
      Object.assign(this.config, ENVIRONMENT_CONFIGS[env]);
    }
    return this;
  }
  
  /**
   * Set performance profile
   */
  performance(profile) {
    if (PERFORMANCE_PROFILES[profile]) {
      Object.assign(this.config, PERFORMANCE_PROFILES[profile]);
    }
    return this;
  }
  
  /**
   * Set crop region from preset
   */
  cropPreset(preset) {
    if (CROP_PRESETS[preset]) {
      this.config.defaultCropRegion = { ...CROP_PRESETS[preset] };
    }
    return this;
  }
  
  /**
   * Set custom crop region
   */
  cropRegion(x, y, width, height) {
    this.config.defaultCropRegion = { x, y, width, height };
    return this;
  }
  
  /**
   * Set analysis threshold
   */
  analysisThreshold(threshold) {
    if (typeof threshold === 'string' && ANALYSIS_THRESHOLDS[threshold]) {
      this.config.analysisThreshold = ANALYSIS_THRESHOLDS[threshold];
    } else if (typeof threshold === 'number') {
      this.config.analysisThreshold = threshold;
    }
    return this;
  }
  
  /**
   * Enable debug mode
   */
  debug(enabled = true) {
    this.config.debug = enabled;
    return this;
  }
  
  /**
   * Set frame rate
   */
  frameRate(rate) {
    this.config.frameRate = rate;
    return this;
  }
  
  /**
   * Set analysis interval
   */
  analysisInterval(interval) {
    this.config.analysisInterval = interval;
    return this;
  }
  
  /**
   * Set maximum subscribers
   */
  maxSubscribers(max) {
    this.config.maxSubscribers = max;
    return this;
  }
  
  /**
   * Custom configuration override
   */
  custom(customConfig) {
    Object.assign(this.config, customConfig);
    return this;
  }
  
  /**
   * Build and return the configuration
   */
  build() {
    return this.validateConfig({ ...this.config });
  }
  
  /**
   * Validate configuration values
   */
  validateConfig(config) {
    // Validate frame rate
    if (config.frameRate < 1 || config.frameRate > 120) {
      console.warn('Frame rate should be between 1-120 fps, using default');
      config.frameRate = DEFAULT_INTERCEPTOR_CONFIG.frameRate;
    }
    
    // Validate analysis interval
    if (config.analysisInterval < 50) {
      console.warn('Analysis interval too low, using minimum 50ms');
      config.analysisInterval = 50;
    }
    
    // Validate max subscribers
    if (config.maxSubscribers < 1 || config.maxSubscribers > 50) {
      console.warn('Max subscribers should be between 1-50, using default');
      config.maxSubscribers = DEFAULT_INTERCEPTOR_CONFIG.maxSubscribers;
    }
    
    // Validate crop region
    if (config.defaultCropRegion) {
      const crop = config.defaultCropRegion;
      if (crop.x < 0 || crop.y < 0 || crop.width <= 0 || crop.height <= 0) {
        console.warn('Invalid crop region, disabling cropping');
        config.defaultCropRegion = null;
      }
    }
    
    return config;
  }
}

/**
 * Quick configuration factory functions
 */
export const createConfig = {
  /**
   * Development configuration
   */
  development: () => new InterceptorConfigBuilder()
    .environment('development')
    .debug(true)
    .build(),
  
  /**
   * Production configuration
   */
  production: () => new InterceptorConfigBuilder()
    .environment('production')
    .performance('standard')
    .build(),
  
  /**
   * Testing configuration
   */
  testing: () => new InterceptorConfigBuilder()
    .environment('testing')
    .debug(true)
    .build(),
  
  /**
   * Screen analysis configuration
   */
  screenAnalysis: (threshold = 'medium') => new InterceptorConfigBuilder()
    .performance('standard')
    .analysisThreshold(threshold)
    .analysisInterval(500)
    .build(),
  
  /**
   * Real-time monitoring configuration
   */
  realtime: () => new InterceptorConfigBuilder()
    .performance('realtime')
    .analysisThreshold('low')
    .debug(false)
    .build(),
  
  /**
   * Low-resource configuration
   */
  lowResource: () => new InterceptorConfigBuilder()
    .performance('lowEnd')
    .analysisThreshold('high')
    .maxSubscribers(2)
    .build(),
  
  /**
   * Custom configuration with crop region
   */
  withCrop: (preset, threshold = 'medium') => new InterceptorConfigBuilder()
    .performance('standard')
    .cropPreset(preset)
    .analysisThreshold(threshold)
    .build(),
};

/**
 * Auto-detect optimal configuration based on device capabilities
 */
export function detectOptimalConfig() {
  const builder = new InterceptorConfigBuilder();
  
  // Detect device performance
  const cores = navigator.hardwareConcurrency || 4;
  const memory = navigator.deviceMemory || 4; // GB
  
  if (cores >= 8 && memory >= 8) {
    builder.performance('highEnd');
  } else if (cores >= 4 && memory >= 4) {
    builder.performance('standard');
  } else {
    builder.performance('lowEnd');
  }
  
  // Detect environment
  const isDev = window.location.hostname === 'localhost' || 
                window.location.hostname === '127.0.0.1';
  
  if (isDev) {
    builder.environment('development');
  } else {
    builder.environment('production');
  }
  
  return builder.build();
}

// Export default configuration
export default DEFAULT_INTERCEPTOR_CONFIG;
