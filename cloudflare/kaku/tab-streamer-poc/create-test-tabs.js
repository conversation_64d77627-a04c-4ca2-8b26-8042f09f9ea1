import { <PERSON>rowser<PERSON>anager } from "./browser-manager.js";
import { ScriptInjector } from "./script-injector.js";

// Connect to existing Chrome instance
const browserManager = new BrowserManager({ port: 9222 });

try {
  // Create script injector
  const scriptInjector = new ScriptInjector(browserManager, null);

  // Create test tabs
  const testUrls = ["https://example.com", "https://httpbin.org/html"];

  for (const url of testUrls) {
    try {
      console.log(`Creating target tab for: ${url}`);
      const targetTab = await browserManager.createTargetTab(url);
      const tabId = targetTab.id;
      console.log(`✅ Target tab created: ${tabId}`);

      // Wait a bit for the page to load
      await new Promise((resolve) => setTimeout(resolve, 2000));

      console.log(`Injecting script into tab: ${tabId}`);
      await scriptInjector.injectTargetTabScript(tabId, "ws://localhost:8081");
      console.log(`✅ Script injected successfully into tab: ${tabId}`);
    } catch (error) {
      console.error(
        `❌ Failed to create/inject tab for ${url}:`,
        error.message
      );
    }
  }

  console.log("✅ Test tabs created and scripts injected");
} catch (error) {
  console.error("❌ Failed to create test tabs:", error);
}
