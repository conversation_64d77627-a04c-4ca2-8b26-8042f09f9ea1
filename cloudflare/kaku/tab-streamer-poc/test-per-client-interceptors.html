<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Per-Client Interceptor Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f0f0f0;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .client-section {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .client-header {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
        }
        .controls {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
            flex-wrap: wrap;
        }
        .control-group {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }
        label {
            font-weight: bold;
            font-size: 12px;
            color: #666;
        }
        input, button {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        button {
            background: #007bff;
            color: white;
            cursor: pointer;
            border: none;
        }
        button:hover {
            background: #0056b3;
        }
        button.toggle-off {
            background: #6c757d;
        }
        .status {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            font-size: 12px;
            color: #666;
        }
        .video-container {
            margin-top: 15px;
        }
        video {
            width: 100%;
            max-width: 400px;
            height: 200px;
            background: #000;
            border-radius: 4px;
        }
        .instructions {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .instructions h3 {
            margin-top: 0;
            color: #0066cc;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Per-Client Video Frame Interceptor Test</h1>
        
        <div class="instructions">
            <h3>Test Instructions:</h3>
            <ol>
                <li>Start the tab streamer POC system</li>
                <li>Open multiple instances of this page in different browser windows/tabs</li>
                <li>Each client below represents an independent web client connection</li>
                <li>Configure different crop regions for each client to test per-client processing</li>
                <li>Verify that each client shows different cropped views of the same source stream</li>
            </ol>
        </div>

        <!-- Client 1 -->
        <div class="client-section">
            <div class="client-header">🖥️ Web Client 1 - Default View</div>
            <div class="controls">
                <div class="control-group">
                    <label>Cropping</label>
                    <button id="toggle-crop-1" onclick="toggleCropping('client1')">Enable Cropping</button>
                </div>
                <div class="control-group">
                    <label>Crop Region (x,y,width,height)</label>
                    <input type="text" id="crop-region-1" placeholder="100,100,400,300" value="100,100,400,300">
                    <button onclick="setCropRegion('client1')">Apply Crop</button>
                </div>
            </div>
            <div class="status" id="status-1">
                Status: Disconnected | Cropping: Disabled | Region: None
            </div>
            <div class="video-container">
                <video id="video-1" autoplay muted playsinline></video>
            </div>
        </div>

        <!-- Client 2 -->
        <div class="client-section">
            <div class="client-header">🖥️ Web Client 2 - Top-Left Crop</div>
            <div class="controls">
                <div class="control-group">
                    <label>Cropping</label>
                    <button id="toggle-crop-2" onclick="toggleCropping('client2')">Enable Cropping</button>
                </div>
                <div class="control-group">
                    <label>Crop Region (x,y,width,height)</label>
                    <input type="text" id="crop-region-2" placeholder="0,0,640,360" value="0,0,640,360">
                    <button onclick="setCropRegion('client2')">Apply Crop</button>
                </div>
            </div>
            <div class="status" id="status-2">
                Status: Disconnected | Cropping: Disabled | Region: None
            </div>
            <div class="video-container">
                <video id="video-2" autoplay muted playsinline></video>
            </div>
        </div>

        <!-- Client 3 -->
        <div class="client-section">
            <div class="client-header">🖥️ Web Client 3 - Center Crop</div>
            <div class="controls">
                <div class="control-group">
                    <label>Cropping</label>
                    <button id="toggle-crop-3" onclick="toggleCropping('client3')">Enable Cropping</button>
                </div>
                <div class="control-group">
                    <label>Crop Region (x,y,width,height)</label>
                    <input type="text" id="crop-region-3" placeholder="320,180,640,360" value="320,180,640,360">
                    <button onclick="setCropRegion('client3')">Apply Crop</button>
                </div>
            </div>
            <div class="status" id="status-3">
                Status: Disconnected | Cropping: Disabled | Region: None
            </div>
            <div class="video-container">
                <video id="video-3" autoplay muted playsinline></video>
            </div>
        </div>

        <div class="instructions">
            <h3>Expected Results:</h3>
            <ul>
                <li><strong>Client 1:</strong> Should show the default crop region (100,100,400,300)</li>
                <li><strong>Client 2:</strong> Should show the top-left portion of the screen (0,0,640,360)</li>
                <li><strong>Client 3:</strong> Should show the center portion of the screen (320,180,640,360)</li>
                <li>Each client should be able to toggle cropping independently</li>
                <li>Changing one client's settings should not affect the others</li>
            </ul>
        </div>
    </div>

    <script>
        // Mock client states for demonstration
        const clientStates = {
            client1: { cropping: false, region: "100,100,400,300" },
            client2: { cropping: false, region: "0,0,640,360" },
            client3: { cropping: false, region: "320,180,640,360" }
        };

        function toggleCropping(clientId) {
            const state = clientStates[clientId];
            state.cropping = !state.cropping;
            
            const button = document.getElementById(`toggle-crop-${clientId.slice(-1)}`);
            button.textContent = state.cropping ? "Disable Cropping" : "Enable Cropping";
            button.className = state.cropping ? "" : "toggle-off";
            
            updateStatus(clientId);
            
            // In real implementation, this would send a message to the signaling server
            console.log(`${clientId}: Cropping ${state.cropping ? 'enabled' : 'disabled'}`);
        }

        function setCropRegion(clientId) {
            const input = document.getElementById(`crop-region-${clientId.slice(-1)}`);
            const region = input.value.trim();
            
            if (!region) {
                alert("Please enter a crop region");
                return;
            }
            
            const parts = region.split(',').map(p => parseInt(p.trim()));
            if (parts.length !== 4 || parts.some(isNaN)) {
                alert("Invalid format. Use: x,y,width,height");
                return;
            }
            
            clientStates[clientId].region = region;
            updateStatus(clientId);
            
            // In real implementation, this would send a message to the signaling server
            console.log(`${clientId}: Crop region set to ${region}`);
        }

        function updateStatus(clientId) {
            const state = clientStates[clientId];
            const statusElement = document.getElementById(`status-${clientId.slice(-1)}`);
            
            statusElement.textContent = `Status: Connected | Cropping: ${state.cropping ? 'Enabled' : 'Disabled'} | Region: ${state.region}`;
        }

        // Initialize UI
        Object.keys(clientStates).forEach(clientId => {
            updateStatus(clientId);
        });

        console.log("Per-client interceptor test page loaded");
        console.log("This page demonstrates how multiple clients can have independent interceptor settings");
    </script>
</body>
</html>
