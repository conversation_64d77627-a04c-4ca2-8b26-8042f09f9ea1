/**
 * Control Tab Script for POC Streaming
 *
 * This script is injected into the control tab to manage WebRTC connections
 * and coordinate streaming between target tabs and web clients
 */

(function () {
  "use strict";

  // Simple CDP implementation for browser environment
  class CDP {
    constructor(targetInfo) {
      this.targetInfo = targetInfo;
      this.ws = null;
      this.messageId = 0;
      this.pendingMessages = new Map();
    }

    async connect() {
      if (this.ws) return;

      this.ws = new WebSocket(this.targetInfo.webSocketDebuggerUrl);

      return new Promise((resolve, reject) => {
        this.ws.onopen = () => {
          console.log(`Connected to target: ${this.targetInfo.title}`);
          resolve();
        };

        this.ws.onerror = reject;

        this.ws.onmessage = (event) => {
          const message = JSON.parse(event.data);

          if (message.id && this.pendingMessages.has(message.id)) {
            const { resolve, reject } = this.pendingMessages.get(message.id);
            this.pendingMessages.delete(message.id);

            if (message.error) {
              reject(new Error(message.error.message));
            } else {
              resolve(message.result);
            }
          }
        };
      });
    }

    async send(method, params = {}, sessionId = null) {
      if (!this.ws) {
        await this.connect();
      }

      const id = ++this.messageId;
      const message = { id, method, params };

      // Add sessionId if provided (for remote debugging)
      if (sessionId) {
        message.sessionId = sessionId;
      }

      return new Promise((resolve, reject) => {
        this.pendingMessages.set(id, { resolve, reject });
        this.ws.send(JSON.stringify(message));
      });
    }

    // Runtime domain
    get Runtime() {
      return {
        enable: (params = {}, sessionId = null) =>
          this.send("Runtime.enable", params, sessionId),
        evaluate: (params, sessionId = null) =>
          this.send("Runtime.evaluate", params, sessionId),
      };
    }

    // Target domain
    get Target() {
      return {
        getTargets: (params = {}, sessionId = null) =>
          this.send("Target.getTargets", params, sessionId),
        createTarget: (params, sessionId = null) =>
          this.send("Target.createTarget", params, sessionId),
        attachToTarget: (params, sessionId = null) =>
          this.send("Target.attachToTarget", params, sessionId),
        closeTarget: (params, sessionId = null) =>
          this.send("Target.closeTarget", params, sessionId),
        activateTarget: (params, sessionId = null) =>
          this.send("Target.activateTarget", params, sessionId),
      };
    }

    // Input domain
    get Input() {
      return {
        dispatchKeyEvent: (params, sessionId = null) =>
          this.send("Input.dispatchKeyEvent", params, sessionId),
        dispatchMouseEvent: (params, sessionId = null) =>
          this.send("Input.dispatchMouseEvent", params, sessionId),
      };
    }

    async close() {
      if (this.ws) {
        this.ws.close();
        this.ws = null;
      }
    }
  }

  console.log("[POC-Streaming] Control tab script initializing...");

  // Prevent multiple injections
  if (window.controlTabInjected) {
    console.log(
      "[POC-Streaming] Control tab script already injected, skipping..."
    );
    return;
  }
  window.controlTabInjected = true;

  class ControlTabManager {
    constructor() {
      this.signalingServerUrl = "ws://localhost:8080"; // Will be set dynamically
      this.websocket = null;
      this.isConnected = false;

      // WebRTC configuration
      this.rtcConfig = {
        iceServers: [
          { urls: "stun:stun.cloudflare.com:3478" },
          { urls: "stun:stun.l.google.com:19302" },
        ],
        iceCandidatePoolSize: 10,
      };

      // Connection management - refactored for multi-client support with grouped structures

      // Grouped WebClient management structure
      this.webClientGroups = new Map(); // webClientId -> { RTCPeerConnection, interceptors, dataChannel, webClient }

      // Grouped Tab management structure
      this.tabGroups = new Map(); // tabId -> { tabInfo, stream }

      // Legacy connections for target tabs (unchanged)
      this.targetConnections = new Map(); // tabId -> RTCPeerConnection (to target tab)

      // Per-client video frame interceptor configurations (unchanged)
      this.clientInterceptorConfigs = new Map(); // webClientId -> { enabled, croppingEnabled, cropRegion }

      // Default interceptor configuration for new clients
      this.defaultInterceptorConfig = {
        enabled: true,
        croppingEnabled: false,
        cropRegion: {
          x: 0,
          y: 0,
          width: window.innerWidth,
          height: window.innerHeight,
        },
      };

      // CDP connection management using simple-cdp
      this.cdpClients = new Map(); // targetTabId -> CDP instance
      this.cdpSessions = new Map(); // targetTabId -> sessionId

      this.init();
    }

    async init() {
      console.log("[POC-Streaming] Initializing control tab manager...");
      await this.connectToSignalingServer();
      this.setupPageListeners();
      this.createControlTabUI();
    }

    /**
     * Initialize a video frame interceptor for a specific client
     */
    initializeClientInterceptor(webClientId) {
      // Get or create client configuration
      let config = this.clientInterceptorConfigs.get(webClientId);
      if (!config) {
        config = { ...this.defaultInterceptorConfig };
        this.clientInterceptorConfigs.set(webClientId, config);
      }

      // Create interceptor instance for this client
      const interceptor = new VideoFrameInterceptor({
        debug: true,
        enableCropping: config.croppingEnabled,
        defaultCropRegion: config.cropRegion,
      });

      // Store interceptor in the grouped structure
      const webClientGroup = this.webClientGroups.get(webClientId);
      if (webClientGroup) {
        webClientGroup.interceptors = [interceptor];
      }

      console.log(
        "[POC-Streaming] Frame interceptor initialized for client:",
        webClientId,
        "with config:",
        config
      );

      return interceptor;
    }

    /**
     * Get interceptor configuration for a specific client
     */
    getClientInterceptorConfig(webClientId) {
      let config = this.clientInterceptorConfigs.get(webClientId);
      if (!config) {
        config = { ...this.defaultInterceptorConfig };
        this.clientInterceptorConfigs.set(webClientId, config);
      }
      return config;
    }

    /**
     * Update interceptor configuration for a specific client
     */
    updateClientInterceptorConfig(webClientId, updates) {
      const config = this.getClientInterceptorConfig(webClientId);
      Object.assign(config, updates);

      // Apply changes to existing interceptor if available
      const webClientGroup = this.webClientGroups.get(webClientId);
      const interceptor = webClientGroup?.interceptors?.[0];
      if (interceptor) {
        if (updates.hasOwnProperty("croppingEnabled")) {
          interceptor.setCroppingEnabled(updates.croppingEnabled);
        }
        if (updates.cropRegion) {
          interceptor.setCropRegion(updates.cropRegion);
        }
      }

      console.log(
        "[POC-Streaming] Updated interceptor config for client:",
        webClientId,
        "new config:",
        config
      );
    }

    /**
     * Process incoming stream through a client-specific frame interceptor
     */
    processStreamForClient(stream, webClientId) {
      const config = this.getClientInterceptorConfig(webClientId);

      if (!config.enabled) return stream;

      try {
        const videoTrack = stream.getVideoTracks()[0];
        if (!videoTrack) return stream;

        // Get or create interceptor for this client from grouped structure
        const webClientGroup = this.webClientGroups.get(webClientId);
        let interceptor = webClientGroup?.interceptors?.[0];
        if (!interceptor) {
          interceptor = this.initializeClientInterceptor(webClientId);
        }

        console.log(
          "[POC-Streaming] Processing stream through interceptor for client:",
          webClientId
        );

        // Initialize the interceptor with the video track
        const processedTrack = interceptor.initialize(videoTrack);

        // Create new stream with processed video track and original audio tracks
        const audioTracks = stream.getAudioTracks();
        const processedStream = new MediaStream([
          processedTrack,
          ...audioTracks,
        ]);
        return processedStream;
      } catch (error) {
        console.error(
          "[POC-Streaming] Error processing stream through interceptor for client:",
          webClientId,
          error
        );
        return stream; // Fallback to original stream
      }
    }

    async connectToSignalingServer() {
      try {
        console.log("[POC-Streaming] Connecting to signaling server...");
        this.websocket = new WebSocket(this.signalingServerUrl);

        this.websocket.onopen = () => {
          console.log(
            "[POC-Streaming] Control tab connected to signaling server"
          );
          this.isConnected = true;

          // Register as control tab
          this.sendMessage({
            type: "register-control-tab",
            metadata: {
              userAgent: navigator.userAgent,
              timestamp: Date.now(),
            },
          });
        };

        this.websocket.onmessage = (event) => {
          try {
            const message = JSON.parse(event.data);
            this.handleMessage(message);
          } catch (error) {
            console.error("[POC-Streaming] Failed to parse message:", error);
          }
        };

        this.websocket.onclose = () => {
          console.log(
            "[POC-Streaming] Control tab disconnected from signaling server"
          );
          this.isConnected = false;
          this.scheduleReconnect();
        };

        this.websocket.onerror = (error) => {
          console.error("[POC-Streaming] Control tab WebSocket error:", error);
        };
      } catch (error) {
        console.error(
          "[POC-Streaming] Failed to connect to signaling server:",
          error
        );
        this.scheduleReconnect();
      }
    }

    scheduleReconnect() {
      console.log("[POC-Streaming] Scheduling reconnection in 5 seconds...");
      setTimeout(() => {
        this.connectToSignalingServer();
      }, 5000);
    }

    sendMessage(message) {
      if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {
        this.websocket.send(JSON.stringify(message));
      } else {
        console.warn("[POC-Streaming] Cannot send message - not connected");
      }
    }

    handleMessage(message) {
      console.log(
        "[POC-Streaming] Control tab received message:",
        message.type
      );

      switch (message.type) {
        case "target-tabs-list":
          this.handleTargetTabsList(message);
          break;
        case "target-tab-registered":
          this.handleTargetTabRegistered(message);
          break;
        case "target-tab-disconnected":
          // this.handleTargetTabDisconnected(message);
          break;
        case "stream-stopped":
          // this.handleStreamStopped(message);
          break;
        case "webrtc-offer":
          this.handleWebRTCOffer(message);
          break;
        case "webrtc-answer":
          this.handleWebRTCAnswer(message);
          break;
        case "webrtc-ice-candidate":
          this.handleWebRTCIceCandidate(message);
          break;
        case "webrtc-offer-from-target":
          this.handleTargetTabOffer(message);
          break;
        case "webrtc-ice-candidate-from-target":
          this.handleTargetTabIceCandidate(message);
          break;
        case "webrtc-answer-from-web-client":
          this.handleWebClientAnswer(message);
          break;
        case "webrtc-ice-candidate-from-web-client":
          this.handleWebClientIceCandidate(message);
          break;
        case "web-client-registered":
          this.handleWebClientRegistered(message);
          break;
        case "web-client-disconnected":
          this.handleWebClientDisconnected(message);
          break;
        case "request-interceptor-status":
          this.handleRequestInterceptorStatus(message);
          break;
        case "toggle-interceptor-cropping":
          this.handleToggleInterceptorCropping(message);
          break;
        case "set-interceptor-crop-region":
          this.handleSetInterceptorCropRegion(message);
          break;
        default:
          console.log("[POC-Streaming] Unknown message type:", message.type);
      }
    }

    handleTargetTabsList(message) {
      console.log(
        "[POC-Streaming] Received target tabs list:",
        message.targetTabs.length
      );

      // Update target tabs using grouped structure
      this.tabGroups.clear();
      message.targetTabs.forEach((tab) => {
        this.tabGroups.set(tab.tabId, {
          tabInfo: tab,
          stream: null,
        });
      });
    }

    handleTargetTabRegistered(message) {
      console.log("[POC-Streaming] Target tab registered:", message.tabId);
      this.tabGroups.set(message.tabId, {
        tabInfo: message,
        stream: null,
      });
    }

    handleTargetTabDisconnected(message) {
      console.log("[POC-Streaming] Target tab disconnected:", message.tabId);

      // Clean up any active streams for this tab
      const tabGroup = this.tabGroups.get(message.tabId);
      if (tabGroup?.stream) {
        this.cleanupStream(message.tabId);
      }

      this.tabGroups.delete(message.tabId);
    }

    handleWebClientRegistered(message) {
      console.log(
        "[POC-Streaming] Web client registered:",
        message.webClientId
      );

      // Initialize grouped structure for this web client
      this.webClientGroups.set(message.webClientId, {
        RTCPeerConnection: null,
        interceptors: [],
        dataChannel: null,
        webClient: {
          clientInfo: message.metadata || {},
          currentTabId: null,
          connected: true,
        },
      });

      // Create dedicated peer connection for this web client
      this.createPeerConnectionForWebClient(message.webClientId);
    }

    handleWebClientDisconnected(message) {
      console.log(
        "[POC-Streaming] Web client disconnected:",
        message.webClientId
      );

      // Clean up peer connection and resources for this client
      this.cleanupWebClient(message.webClientId);
    }

    /**
     * Handle interceptor status request from web client
     */
    handleRequestInterceptorStatus(message) {
      console.log(
        "[POC-Streaming] Interceptor status request from web client:",
        message.webClientId,
        "for tab:",
        message.tabId
      );

      // Get client-specific interceptor configuration
      const config = this.getClientInterceptorConfig(message.webClientId);

      // Send status back to web client via signaling server
      this.sendMessage({
        type: "interceptor-status-to-web-client",
        webClientId: message.webClientId,
        enabled: config.enabled,
        croppingEnabled: config.croppingEnabled,
        cropRegion: config.cropRegion,
      });
    }

    /**
     * Handle toggle cropping request from web client
     */
    handleToggleInterceptorCropping(message) {
      console.log(
        "[POC-Streaming] Toggle cropping request from web client:",
        message.webClientId,
        "enabled:",
        message.enabled
      );

      // Update client-specific configuration
      this.updateClientInterceptorConfig(message.webClientId, {
        croppingEnabled: message.enabled,
      });

      // Get updated configuration
      const config = this.getClientInterceptorConfig(message.webClientId);

      // Send updated status back to web client
      this.sendMessage({
        type: "interceptor-status-to-web-client",
        webClientId: message.webClientId,
        enabled: config.enabled,
        croppingEnabled: config.croppingEnabled,
        cropRegion: config.cropRegion,
      });
    }

    /**
     * Handle set crop region request from web client
     */
    handleSetInterceptorCropRegion(message) {
      console.log(
        "[POC-Streaming] Set crop region request from web client:",
        message.webClientId,
        "region:",
        message.cropRegion
      );

      // Update client-specific configuration
      this.updateClientInterceptorConfig(message.webClientId, {
        cropRegion: message.cropRegion,
      });

      // Get updated configuration
      const config = this.getClientInterceptorConfig(message.webClientId);

      // Send updated status back to web client
      this.sendMessage({
        type: "interceptor-status-to-web-client",
        webClientId: message.webClientId,
        enabled: config.enabled,
        croppingEnabled: config.croppingEnabled,
        cropRegion: config.cropRegion,
      });
    }

    handleStreamStopped(message) {
      console.log(
        "[POC-Streaming] Stream stopped for tab:",
        message.targetTabId
      );
      this.cleanupStream(message.targetTabId);
    }

    async handleWebRTCOffer() {
      console.log("[POC-Streaming] Received WebRTC offer");
      // This would be handled if control tab receives offers (not typical in this architecture)
    }

    async handleWebRTCAnswer(message) {
      console.log(
        "[POC-Streaming] Received WebRTC answer for web client:",
        message.webClientId
      );

      const webClientGroup = this.webClientGroups.get(message.webClientId);
      const peerConnection = webClientGroup?.RTCPeerConnection;
      if (peerConnection) {
        try {
          await peerConnection.setRemoteDescription(
            new RTCSessionDescription(message.answer)
          );
          console.log(
            "[POC-Streaming] WebRTC connection established for web client:",
            message.webClientId
          );
        } catch (error) {
          console.error(
            "[POC-Streaming] Failed to set remote description:",
            error
          );
        }
      }
    }

    async handleWebRTCIceCandidate(message) {
      const webClientGroup = this.webClientGroups.get(message.webClientId);
      const peerConnection = webClientGroup?.RTCPeerConnection;
      if (peerConnection) {
        try {
          await peerConnection.addIceCandidate(message.candidate);
        } catch (error) {
          console.error("[POC-Streaming] Failed to add ICE candidate:", error);
        }
      }
    }

    async handleTargetTabOffer(message) {
      console.log(
        "[POC-Streaming] Received WebRTC offer from target tab:",
        message
      );

      // Create stream info from the message since target tab is initiating
      const streamInfo = {
        targetTabId: message.targetTabId,
        status: "connecting",
      };

      // Create peer connection to target tab
      const targetPeerConnection = new RTCPeerConnection(this.rtcConfig);

      // Store target connection using tabId as key
      this.targetConnections.set(message.targetTabId, targetPeerConnection);

      // Handle incoming stream from target tab
      targetPeerConnection.ontrack = (event) => {
        console.log("[POC-Streaming] Target stream event:", event);
        const [stream] = event.streams;

        // Store the stream in the grouped tab structure
        const tabGroup = this.tabGroups.get(message.targetTabId);
        if (tabGroup) {
          tabGroup.stream = stream;
        }

        console.log(
          `[POC-Streaming] Stored active stream for tab ${message.targetTabId}`
        );

        // Display the original stream in control tab (no processing needed here)
        this.displayStreamInControlTab(message.targetTabId, stream, streamInfo);

        // Broadcast original stream to ALL connected web clients (each will process individually)
        this.broadcastStreamToAllClients(stream, message.targetTabId);
      };

      // Handle ICE candidates from target tab
      targetPeerConnection.onicecandidate = (event) => {
        if (event.candidate) {
          // Send ICE candidate back to target tab
          this.sendMessage({
            type: "webrtc-ice-candidate-to-target",
            candidate: event.candidate,
            targetTabId: streamInfo.targetTabId,
          });
        }
      };

      // Accept the offer from target tab
      try {
        await targetPeerConnection.setRemoteDescription(
          new RTCSessionDescription(message.offer)
        );
        const answer = await targetPeerConnection.createAnswer();
        await targetPeerConnection.setLocalDescription(answer);

        // Send answer back to target tab
        this.sendMessage({
          type: "webrtc-answer-to-target",
          answer: answer,
          targetTabId: streamInfo.targetTabId,
        });

        console.log("[POC-Streaming] WebRTC answer sent to target tab");
      } catch (error) {
        console.error(
          "[POC-Streaming] Failed to handle target tab offer:",
          error
        );
      }
    }

    async handleTargetTabIceCandidate(message) {
      const targetPeerConnection = this.targetConnections.get(
        message.targetTabId
      );
      if (targetPeerConnection) {
        try {
          await targetPeerConnection.addIceCandidate(message.candidate);
        } catch (error) {
          console.error(
            "[POC-Streaming] Failed to add target ICE candidate:",
            error
          );
        }
      }
    }

    async handleWebClientAnswer(message) {
      console.log(
        "[POC-Streaming] Received WebRTC answer from web client:",
        message.webClientId
      );

      const webClientGroup = this.webClientGroups.get(message.webClientId);
      const peerConnection = webClientGroup?.RTCPeerConnection;
      if (peerConnection) {
        try {
          await peerConnection.setRemoteDescription(
            new RTCSessionDescription(message.answer)
          );
          console.log(
            "[POC-Streaming] WebRTC connection established with web client:",
            message.webClientId
          );
        } catch (error) {
          console.error(
            "[POC-Streaming] Failed to set remote description from web client:",
            error
          );
        }
      } else {
        console.warn(
          "[POC-Streaming] No peer connection found for web client:",
          message.webClientId
        );
      }
    }

    async handleWebClientIceCandidate(message) {
      console.log(
        "[POC-Streaming] Received ICE candidate from web client:",
        message.webClientId
      );

      const webClientGroup = this.webClientGroups.get(message.webClientId);
      const peerConnection = webClientGroup?.RTCPeerConnection;
      if (peerConnection) {
        try {
          await peerConnection.addIceCandidate(message.candidate);
        } catch (error) {
          console.error(
            "[POC-Streaming] Failed to add ICE candidate from web client:",
            error
          );
        }
      } else {
        console.warn(
          "[POC-Streaming] No peer connection found for web client:",
          message.webClientId
        );
      }
    }

    createPeerConnectionForWebClient(webClientId) {
      console.log(
        "[POC-Streaming] Creating peer connection for web client:",
        webClientId
      );

      const peerConnection = new RTCPeerConnection(this.rtcConfig);

      // Create data channel for user events
      const dataChannel = peerConnection.createDataChannel("userEvents", {
        ordered: true,
      });
      this.setupDataChannelHandlers(dataChannel, webClientId);

      // Handle ICE candidates from web client
      peerConnection.onicecandidate = (event) => {
        if (event.candidate) {
          this.sendMessage({
            type: "webrtc-ice-candidate-to-web-client",
            candidate: event.candidate,
            webClientId: webClientId,
          });
        }
      };

      // Store the peer connection and data channel in the grouped structure
      const webClientGroup = this.webClientGroups.get(webClientId);
      if (webClientGroup) {
        webClientGroup.RTCPeerConnection = peerConnection;
        webClientGroup.dataChannel = dataChannel;
      }

      // Check for existing active streams and add tracks immediately
      this.addExistingTracksToNewClient(webClientId, peerConnection);

      console.log(
        "[POC-Streaming] Peer connection created for web client:",
        webClientId
      );
    }

    /**
     * Add existing active tracks to a newly connected web client
     */
    addExistingTracksToNewClient(webClientId, peerConnection) {
      console.log(
        "[POC-Streaming] Checking for existing active streams for new client:",
        webClientId
      );

      // Check all tab groups for active streams
      for (const [tabId, tabGroup] of this.tabGroups) {
        if (tabGroup.stream) {
          console.log(
            `[POC-Streaming] Found active stream for tab ${tabId}, adding tracks to new client ${webClientId}`
          );

          // Get the stored stream
          const originalStream = tabGroup.stream;
          if (originalStream && originalStream.getTracks().length > 0) {
            // Process the stream for this specific client
            const clientProcessedStream = this.processStreamForClient(
              originalStream,
              webClientId
            );

            // Update client's current tab
            const webClientGroup = this.webClientGroups.get(webClientId);
            if (webClientGroup?.webClient) {
              webClientGroup.webClient.currentTabId = tabId;
            }

            // Add tracks to the peer connection
            clientProcessedStream.getTracks().forEach((track) => {
              console.log(
                `[POC-Streaming] Adding existing ${track.kind} track to new client:`,
                webClientId
              );
              peerConnection.addTrack(track, clientProcessedStream);
            });

            this.createOfferToWebClient(tabId, webClientId);

            console.log(
              `[POC-Streaming] Added existing stream tracks from tab ${tabId} to new client ${webClientId}`
            );

            // Only add tracks from the first active stream to avoid conflicts
            break;
          }
        }
      }

      // Check if no active streams were found
      const hasActiveStreams = Array.from(this.tabGroups.values()).some(
        (tabGroup) => tabGroup.stream
      );
      if (!hasActiveStreams) {
        console.log(
          "[POC-Streaming] No existing active streams found for new client:",
          webClientId
        );
      }
    }

    cleanupWebClient(webClientId) {
      console.log("[POC-Streaming] Cleaning up web client:", webClientId);

      // Get the grouped structure for this client
      const webClientGroup = this.webClientGroups.get(webClientId);

      if (webClientGroup) {
        // Close and remove peer connection
        if (webClientGroup.RTCPeerConnection) {
          webClientGroup.RTCPeerConnection.close();
        }

        // Clean up data channel
        if (webClientGroup.dataChannel) {
          webClientGroup.dataChannel.close();
        }

        // Clean up client interceptors
        if (webClientGroup.interceptors) {
          webClientGroup.interceptors.forEach((interceptor) => {
            // Clean up interceptor resources if needed
          });
        }
      }

      // Remove client interceptor configuration
      this.clientInterceptorConfigs.delete(webClientId);

      // Remove from web client groups map
      this.webClientGroups.delete(webClientId);

      console.log("[POC-Streaming] Web client cleanup completed:", webClientId);
    }

    broadcastStreamToAllClients(stream, targetTabId) {
      console.log(
        "[POC-Streaming] Broadcasting stream to all connected web clients for tab:",
        targetTabId
      );

      // Iterate through all connected web clients using grouped structure
      for (const [webClientId, webClientGroup] of this.webClientGroups) {
        const peerConnection = webClientGroup.RTCPeerConnection;
        if (!peerConnection) continue;

        console.log(
          "[POC-Streaming] Processing web client:",
          webClientId,
          "- Connection state:",
          peerConnection.connectionState
        );

        // Update client's current tab
        if (webClientGroup.webClient) {
          webClientGroup.webClient.currentTabId = targetTabId;
        }

        // Process stream individually for this client
        const clientProcessedStream = this.processStreamForClient(
          stream,
          webClientId
        );

        // Add or replace tracks for this client with their processed stream
        clientProcessedStream.getTracks().forEach((track) => {
          const sender = peerConnection
            .getSenders()
            .find((s) => s.track && s.track.kind === track.kind);

          if (sender) {
            console.log(
              `[POC-Streaming] Replacing ${track.kind} track for client:`,
              webClientId
            );
            sender.replaceTrack(track);
          } else {
            console.log(
              `[POC-Streaming] Adding ${track.kind} track for client:`,
              webClientId
            );
            peerConnection.addTrack(track, clientProcessedStream);
          }
        });

        // Check connection state before creating new offer
        const connectionState = peerConnection.connectionState;
        const signalingState = peerConnection.signalingState;

        if (
          connectionState === "connected" ||
          connectionState === "completed"
        ) {
          console.log(
            `[POC-Streaming] Client ${webClientId} already connected - tracks updated via existing connection`
          );
        } else if (
          signalingState === "stable" &&
          (connectionState === "new" || connectionState === "connecting")
        ) {
          console.log(
            `[POC-Streaming] Creating new offer for client ${webClientId} (connection: ${connectionState}, signaling: ${signalingState})`
          );
          this.createOfferToWebClient(targetTabId, webClientId);
        } else if (signalingState !== "stable") {
          console.log(
            `[POC-Streaming] Skipping offer for client ${webClientId} - negotiation in progress (signaling: ${signalingState})`
          );
        } else {
          console.log(
            `[POC-Streaming] Creating offer for client ${webClientId} (connection: ${connectionState}, signaling: ${signalingState})`
          );
          this.createOfferToWebClient(targetTabId, webClientId);
        }
      }

      console.log(
        "[POC-Streaming] Stream broadcast completed to",
        this.webClientGroups.size,
        "clients"
      );
    }

    async createOfferToWebClient(tabId, webClientId) {
      const webClientGroup = this.webClientGroups.get(webClientId);
      const peerConnection = webClientGroup?.RTCPeerConnection;
      if (!peerConnection) {
        console.error(
          "[POC-Streaming] No peer connection found for web client:",
          webClientId
        );
        return;
      }

      // Double-check connection state before creating offer
      const connectionState = peerConnection.connectionState;
      const signalingState = peerConnection.signalingState;

      console.log(
        `[POC-Streaming] Creating offer for client ${webClientId} - Connection: ${connectionState}, Signaling: ${signalingState}`
      );

      try {
        const offer = await peerConnection.createOffer();
        await peerConnection.setLocalDescription(offer);

        // Send offer to web client
        this.sendMessage({
          type: "webrtc-offer-to-web-client",
          offer: offer,
          targetClientId: webClientId,
          tabId: tabId,
          fromClientId: this.clientId,
        });

        console.log(
          `[POC-Streaming] WebRTC offer sent to web client: ${webClientId} (${connectionState} -> negotiating)`
        );
      } catch (error) {
        console.error(
          `[POC-Streaming] Failed to create offer to web client ${webClientId}:`,
          error.message
        );

        // Log additional context for debugging
        console.error(
          `[POC-Streaming] Error context - Connection: ${connectionState}, Signaling: ${signalingState}`
        );
      }
    }

    cleanupStream(tabId) {
      console.log("[POC-Streaming] Cleaning up stream for tab:", tabId);

      // Clean up target connection
      const targetConnection = this.targetConnections.get(tabId);
      if (targetConnection) {
        targetConnection.close();
        this.targetConnections.delete(tabId);
      }

      // Update all web clients to remove tracks from this tab
      for (const [webClientId, webClientGroup] of this.webClientGroups) {
        if (webClientGroup.webClient?.currentTabId === tabId) {
          webClientGroup.webClient.currentTabId = null;

          // Remove tracks from this client's peer connection
          const peerConnection = webClientGroup.RTCPeerConnection;
          if (peerConnection) {
            peerConnection.getSenders().forEach((sender) => {
              if (sender.track) {
                peerConnection.removeTrack(sender);
              }
            });
          }
        }
      }

      // Remove from UI
      const streamElement = document.getElementById(`poc-stream-${tabId}`);
      if (streamElement) {
        streamElement.remove();
      }

      // Add "no streams" message if no streams left
      const streamsContainer = document.getElementById("poc-streams-container");
      if (streamsContainer && streamsContainer.children.length === 0) {
        streamsContainer.innerHTML =
          '<div style="color: #666; font-style: italic;">No active streams</div>';
      }

      // Clear stream from tab group
      const tabGroup = this.tabGroups.get(tabId);
      if (tabGroup) {
        tabGroup.stream = null;
      }
    }

    createControlTabUI() {
      // Create a floating control panel for the control tab
      const controlPanel = document.createElement("div");
      controlPanel.id = "poc-control-panel";
      controlPanel.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        width: 400px;
        max-height: 600px;
        background: rgba(0, 0, 0, 0.9);
        color: white;
        border-radius: 8px;
        padding: 16px;
        z-index: 10000;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        font-size: 14px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        overflow-y: auto;
      `;

      controlPanel.innerHTML = `
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
          <h3 style="margin: 0; color: #4CAF50;">POC Control Tab</h3>
          <button id="poc-toggle-panel" style="background: none; border: 1px solid #666; color: white; padding: 4px 8px; border-radius: 4px; cursor: pointer;">−</button>
        </div>
        <div id="poc-panel-content">
          <div style="margin-bottom: 12px;">
            <div style="font-weight: bold; margin-bottom: 4px;">Status:</div>
            <div id="poc-connection-status" style="color: #ff9800;">Connecting...</div>
          </div>
          <div style="margin-bottom: 12px;">
            <div style="font-weight: bold; margin-bottom: 4px;">Active Streams:</div>
            <div id="poc-streams-container" style="max-height: 300px; overflow-y: auto;">
              <div style="color: #666; font-style: italic;">No active streams</div>
            </div>
          </div>
          <div style="margin-bottom: 12px;">
            <div style="font-weight: bold; margin-bottom: 4px;">Target Tabs:</div>
            <div id="poc-tabs-container" style="max-height: 200px; overflow-y: auto;">
              <div style="color: #666; font-style: italic;">No target tabs</div>
            </div>
          </div>
          <div style="margin-bottom: 12px;">
            <div style="font-weight: bold; margin-bottom: 4px;">Frame Interceptor:</div>
            <div id="poc-interceptor-container">
              <div style="color: #666; font-style: italic;">No active interceptor</div>
            </div>
          </div>
        </div>
      `;

      document.body.appendChild(controlPanel);

      // Add toggle functionality
      const toggleBtn = document.getElementById("poc-toggle-panel");
      const panelContent = document.getElementById("poc-panel-content");
      let isCollapsed = false;

      toggleBtn.addEventListener("click", () => {
        isCollapsed = !isCollapsed;
        panelContent.style.display = isCollapsed ? "none" : "block";
        toggleBtn.textContent = isCollapsed ? "+" : "−";
        controlPanel.style.height = isCollapsed ? "auto" : "";
      });

      // Update connection status
      this.updateConnectionStatus("Connected");
    }

    updateConnectionStatus(status) {
      const statusElement = document.getElementById("poc-connection-status");
      if (statusElement) {
        statusElement.textContent = status;
        statusElement.style.color =
          status === "Connected" ? "#4CAF50" : "#ff9800";
      }
    }

    displayStreamInControlTab(tabId, mediaStream, streamInfo) {
      console.log(
        "[POC-Streaming] Displaying stream in control tab for tab:",
        tabId
      );

      const streamsContainer = document.getElementById("poc-streams-container");
      if (!streamsContainer) {
        console.warn("[POC-Streaming] Streams container not found");
        return;
      }

      // Remove "no streams" message if present
      const noStreamsMsg = streamsContainer.querySelector(
        '[style*="font-style: italic"]'
      );
      if (
        noStreamsMsg &&
        noStreamsMsg.textContent.includes("No active streams")
      ) {
        noStreamsMsg.remove();
      }

      // Create stream display element
      const streamElement = document.createElement("div");
      streamElement.id = `poc-stream-${tabId}`;
      streamElement.style.cssText = `
        margin-bottom: 12px;
        padding: 8px;
        border: 1px solid #333;
        border-radius: 4px;
        background: rgba(255, 255, 255, 0.05);
      `;

      streamElement.innerHTML = `
        <div style="font-weight: bold; margin-bottom: 8px; color: #4CAF50;">
          📺 Tab ${tabId.substring(0, 8)}...
        </div>
        <video
          id="poc-video-${tabId}"
          autoplay
          muted
          playsinline
          style="width: 100%; height: 150px; background: #000; border-radius: 4px; object-fit: contain;"
        ></video>
        <div style="margin-top: 8px; font-size: 12px; color: #ccc;">
          Target: ${streamInfo.targetTabId || "Unknown"}
        </div>
      `;

      streamsContainer.appendChild(streamElement);

      // Set the video source
      const video = document.getElementById(`poc-video-${tabId}`);
      if (video && mediaStream) {
        video.srcObject = mediaStream;

        video.onloadedmetadata = () => {
          console.log("[POC-Streaming] Video metadata loaded in control tab");
        };

        video.onplay = () => {
          console.log("[POC-Streaming] Video started playing in control tab");
        };

        video.onerror = (error) => {
          console.error("[POC-Streaming] Video error in control tab:", error);
        };
      }
    }

    setupPageListeners() {
      // Listen for page unload
      window.addEventListener("beforeunload", () => {
        console.log("[POC-Streaming] Control tab unloading...");

        // Clean up all streams
        for (const [tabId, tabGroup] of this.tabGroups) {
          if (tabGroup.stream) {
            this.cleanupStream(tabId);
          }
        }

        // Close WebSocket
        if (this.websocket) {
          this.websocket.close();
        }
      });
    }

    /**
     * Setup data channel handlers for user event replay
     */
    setupDataChannelHandlers(dataChannel, webClientId) {
      console.log(
        "[POC-Streaming] Setting up data channel for web client:",
        webClientId
      );

      dataChannel.onopen = () => {
        console.log(
          "[POC-Streaming] Data channel opened for web client:",
          webClientId
        );
      };

      dataChannel.onclose = () => {
        console.log(
          "[POC-Streaming] Data channel closed for web client:",
          webClientId
        );
      };

      dataChannel.onerror = (error) => {
        console.error("[POC-Streaming] Data channel error:", error);
      };

      dataChannel.onmessage = (event) => {
        try {
          const userEvent = JSON.parse(event.data);
          console.log("[POC-Streaming] Received user event:", userEvent);

          // Get the current tab this client is viewing from grouped structure
          const webClientGroup = this.webClientGroups.get(webClientId);
          const targetTabId = webClientGroup?.webClient?.currentTabId;

          if (targetTabId) {
            this.handleUserEvent(userEvent, targetTabId);
          } else {
            console.warn(
              "[POC-Streaming] No target tab for user event from client:",
              webClientId
            );
          }
        } catch (error) {
          console.error("[POC-Streaming] Failed to parse user event:", error);
        }
      };
    }

    /**
     * Handle user events from web client
     */
    async handleUserEvent(userEvent, targetTabId) {
      if (userEvent.type !== "user-event") {
        console.warn("[POC-Streaming] Unknown event type:", userEvent.type);
        return;
      }

      console.log(
        "[POC-Streaming] Replaying user event on target tab:",
        targetTabId
      );

      try {
        await this.replayEventOnTargetTab(userEvent, targetTabId);
      } catch (error) {
        console.error("[POC-Streaming] Failed to replay event:", error);
      }
    }

    /**
     * Establish persistent CDP session to target tab using simple-cdp
     */
    async establishCDPSession(targetTabId) {
      if (this.cdpClients.has(targetTabId)) {
        return this.cdpClients.get(targetTabId);
      }

      try {
        console.log(
          "[POC-Streaming] Establishing CDP session to tab:",
          targetTabId
        );

        // Get target info from CDP
        const tabsResponse = await fetch("http://localhost:9222/json");
        const tabs = await tabsResponse.json();
        const targetTab = tabs.find((tab) => tab.id === targetTabId);

        if (!targetTab) {
          throw new Error(`Target tab ${targetTabId} not found`);
        }

        // Create CDP client instance
        const cdpClient = new CDP(targetTab);
        await cdpClient.connect();

        // Attach to target to create a session
        const attachResult = await cdpClient.Target.attachToTarget({
          targetId: targetTabId,
          flatten: true,
        });

        const sessionId = attachResult.sessionId;
        console.log("[POC-Streaming] CDP session established:", sessionId);

        // Store the client and session
        this.cdpClients.set(targetTabId, cdpClient);
        this.cdpSessions.set(targetTabId, sessionId);

        return cdpClient;
      } catch (error) {
        console.error(
          "[POC-Streaming] Failed to establish CDP session:",
          error
        );
        throw error;
      }
    }

    /**
     * Execute CDP Input command using simple-cdp
     */
    async executeCDPInputCommand(targetTabId, method, params) {
      try {
        const cdpClient = await this.establishCDPSession(targetTabId);
        const sessionId = this.cdpSessions.get(targetTabId);

        console.log(
          `[POC-Streaming] Executing ${method} on target tab:`,
          targetTabId
        );

        // Use simple-cdp to execute the Input command
        if (method === "Input.dispatchMouseEvent") {
          return await cdpClient.Input.dispatchMouseEvent(params, sessionId);
        } else if (method === "Input.dispatchKeyEvent") {
          return await cdpClient.Input.dispatchKeyEvent(params, sessionId);
        } else {
          throw new Error(`Unsupported Input method: ${method}`);
        }
      } catch (error) {
        console.error(
          "[POC-Streaming] Failed to execute CDP Input command:",
          error
        );
        throw error;
      }
    }

    /**
     * Replay user event on target tab using CDP
     */
    async replayEventOnTargetTab(userEvent, targetTabId) {
      if (userEvent.eventType === "click") {
        await this.replayClickEvent(userEvent, targetTabId);
      } else {
        console.warn(
          "[POC-Streaming] Unsupported event type:",
          userEvent.eventType
        );
      }
    }

    /**
     * Replay click event on target tab
     */
    async replayClickEvent(userEvent, targetTabId) {
      try {
        // Get target tab dimensions
        const tabInfo = await this.getTargetTabInfo(targetTabId);
        if (!tabInfo) {
          console.error("[POC-Streaming] Could not get target tab info");
          return;
        }

        // Calculate actual coordinates in target tab
        const targetX = userEvent.x * tabInfo.width;
        const targetY = userEvent.y * tabInfo.height;

        console.log(
          `[POC-Streaming] Replaying click at (${targetX}, ${targetY}) on tab ${targetTabId}`
        );

        // Use CDP Input API for more reliable event dispatch
        console.log("[POC-Streaming] Using CDP Input API for click event");

        // Dispatch mouse down
        await this.executeCDPInputCommand(
          targetTabId,
          "Input.dispatchMouseEvent",
          {
            type: "mousePressed",
            x: Math.round(targetX),
            y: Math.round(targetY),
            button: "left",
            clickCount: 1,
            buttons: 1,
          }
        );

        // Small delay between mouse down and up
        await new Promise((resolve) => setTimeout(resolve, 50));

        // Dispatch mouse up
        await this.executeCDPInputCommand(
          targetTabId,
          "Input.dispatchMouseEvent",
          {
            type: "mouseReleased",
            x: Math.round(targetX),
            y: Math.round(targetY),
            button: "left",
            clickCount: 1,
            buttons: 0,
          }
        );

        console.log(
          "[POC-Streaming] Click event dispatched successfully via CDP Input API"
        );
      } catch (error) {
        console.error("[POC-Streaming] Failed to replay click:", error);
      }
    }

    /**
     * Get target tab information using simple-cdp
     */
    async getTargetTabInfo(targetTabId) {
      try {
        console.log("[POC-Streaming] Getting tab info for:", targetTabId);

        const cdpClient = await this.establishCDPSession(targetTabId);
        const sessionId = this.cdpSessions.get(targetTabId);

        // Use simple-cdp to evaluate script and get tab dimensions
        const result = await cdpClient.Runtime.evaluate(
          {
            expression: `({
              width: window.innerWidth,
              height: window.innerHeight,
              url: window.location.href
            })`,
            returnByValue: true,
            awaitPromise: true,
          },
          sessionId
        );
        return result.result.value;
      } catch (error) {
        console.error("[POC-Streaming] Failed to get tab info:", error);
        return { width: 1920, height: 1080, url: "unknown" };
      }
    }

    /**
     * Execute script on target tab using simple-cdp
     */
    async executeCDPScript(targetTabId, script) {
      try {
        const cdpClient = await this.establishCDPSession(targetTabId);
        const sessionId = this.cdpSessions.get(targetTabId);

        console.log(
          "[POC-Streaming] Executing script on target tab:",
          targetTabId
        );

        // Use simple-cdp to evaluate script
        const result = await cdpClient.Runtime.evaluate(
          {
            expression: script,
            returnByValue: true,
          },
          sessionId
        );

        return result.result.value;
      } catch (error) {
        console.error("[POC-Streaming] Failed to execute CDP script:", error);
        return null;
      }
    }

    /**
     * Clean up CDP session for a target tab
     */
    async cleanupCDPSession(targetTabId) {
      try {
        const cdpClient = this.cdpClients.get(targetTabId);
        if (cdpClient) {
          console.log(
            "[POC-Streaming] Cleaning up CDP session for tab:",
            targetTabId
          );
          await cdpClient.close();
          this.cdpClients.delete(targetTabId);
          this.cdpSessions.delete(targetTabId);
        }
      } catch (error) {
        console.error("[POC-Streaming] Failed to cleanup CDP session:", error);
      }
    }

    /**
     * Toggle interceptor for a target tab
     */
    toggleInterceptor(tabId) {
      console.log(
        "[POC-Streaming] Interceptor controls are now per-client. Use the web client interface."
      );
    }

    /**
     * Toggle cropping for a target tab
     */
    toggleCropping(tabId) {
      console.log(
        "[POC-Streaming] Cropping controls are now per-client. Use the web client interface."
      );
    }

    /**
     * Set crop region for a target tab
     */
    setCropRegion(tabId) {
      console.log(
        "[POC-Streaming] Crop region controls are now per-client. Use the web client interface."
      );
    }
  }

  // Initialize the control tab manager
  window.pocControlTabManager = new ControlTabManager();

  console.log("[POC-Streaming] Control tab script initialized successfully");
})();
