/**
 * Video Frame Interceptor
 * 
 * A modular interceptor that sits between video track capture and WebRTC peer connection.
 * Provides frame cropping and subscription capabilities for real-time frame analysis.
 * 
 * Features:
 * - Frame cropping with configurable crop regions
 * - Event-driven subscription API for frame listeners
 * - Toggle-able interceptor functionality
 * - Maintains existing streaming functionality
 * - Support for multiple frame subscribers
 */

class VideoFrameInterceptor {
  constructor(options = {}) {
    this.options = {
      debug: options.debug || false,
      defaultCropRegion: options.defaultCropRegion || null,
      frameRate: options.frameRate || 30,
      enableCropping: options.enableCropping !== false, // Default to true
      ...options
    };

    // State management
    this.isEnabled = true;
    this.cropRegion = this.options.defaultCropRegion;
    this.subscribers = new Map(); // subscriberId -> callback
    this.frameCount = 0;
    this.lastFrameTime = 0;
    
    // Transform stream components
    this.processor = null;
    this.generator = null;
    this.transformStream = null;
    
    // Original track reference
    this.originalTrack = null;
    this.processedTrack = null;

    this.log('VideoFrameInterceptor initialized', this.options);
  }

  /**
   * Initialize the interceptor with a video track
   * @param {MediaStreamTrack} videoTrack - The original video track to process
   * @returns {MediaStreamTrack} - The processed video track
   */
  initialize(videoTrack) {
    if (!videoTrack || videoTrack.kind !== 'video') {
      throw new Error('VideoFrameInterceptor requires a valid video track');
    }

    this.originalTrack = videoTrack;
    this.log('Initializing interceptor with video track:', videoTrack.label);

    // Create processor and generator
    this.processor = new MediaStreamTrackProcessor({ track: videoTrack });
    this.generator = new MediaStreamTrackGenerator({ kind: 'video' });

    // Create transform stream
    this.transformStream = new TransformStream({
      transform: this.transformFrame.bind(this)
    });

    // Connect the pipeline
    this.processor.readable
      .pipeThrough(this.transformStream)
      .pipeTo(this.generator.writable)
      .catch(error => {
        this.log('Pipeline error:', error);
      });

    this.processedTrack = this.generator;
    this.log('Interceptor pipeline established');

    return this.processedTrack;
  }

  /**
   * Transform function for processing individual frames
   * @param {VideoFrame} frame - Input video frame
   * @param {TransformStreamDefaultController} controller - Transform controller
   */
  async transformFrame(frame, controller) {
    try {
      this.frameCount++;
      const currentTime = performance.now();
      
      // Throttle debug logging
      if (this.options.debug && (currentTime - this.lastFrameTime) > 1000) {
        this.log(`Processed ${this.frameCount} frames`);
        this.lastFrameTime = currentTime;
      }

      let processedFrame = frame;

      // Apply cropping if enabled and crop region is set
      if (this.isEnabled && this.options.enableCropping && this.cropRegion) {
        processedFrame = this.applyCropping(frame);
      }

      // Notify subscribers with the processed frame (before sending to controller)
      if (this.subscribers.size > 0) {
        await this.notifySubscribers(processedFrame);
      }

      // Send frame to the output stream (to WebRTC peer connection)
      if (this.isEnabled) {
        controller.enqueue(processedFrame);
      } else {
        // If interceptor is disabled, pass through original frame
        controller.enqueue(frame);
      }

      // Clean up the original frame if we created a new one
      if (processedFrame !== frame) {
        frame.close();
      }

    } catch (error) {
      this.log('Error processing frame:', error);
      // Pass through original frame on error
      controller.enqueue(frame);
    }
  }

  /**
   * Apply cropping to a video frame
   * @param {VideoFrame} frame - Input frame
   * @returns {VideoFrame} - Cropped frame
   */
  applyCropping(frame) {
    const { codedWidth, codedHeight } = frame;
    
    // Ensure crop region is within frame bounds and aligned for YUV 4:2:0
    const safeCropRegion = {
      x: this.makeEven(Math.max(0, Math.min(this.cropRegion.x, codedWidth))),
      y: this.makeEven(Math.max(0, Math.min(this.cropRegion.y, codedHeight))),
      width: this.makeEven(Math.max(2, Math.min(this.cropRegion.width, codedWidth - this.cropRegion.x))),
      height: this.makeEven(Math.max(2, Math.min(this.cropRegion.height, codedHeight - this.cropRegion.y)))
    };

    try {
      const croppedFrame = new VideoFrame(frame, {
        visibleRect: safeCropRegion,
        displayWidth: safeCropRegion.width,
        displayHeight: safeCropRegion.height,
        timestamp: frame.timestamp,
        duration: frame.duration,
      });

      this.log('Frame cropped:', safeCropRegion);
      return croppedFrame;
    } catch (error) {
      this.log('Cropping failed, using original frame:', error);
      return frame;
    }
  }

  /**
   * Notify all subscribers with the processed frame
   * @param {VideoFrame} frame - Processed frame to send to subscribers
   */
  async notifySubscribers(frame) {
    const promises = [];
    
    for (const [subscriberId, callback] of this.subscribers) {
      try {
        // Clone frame for each subscriber to prevent conflicts
        const frameClone = new VideoFrame(frame, {
          timestamp: frame.timestamp,
          duration: frame.duration
        });
        
        const result = callback(frameClone, {
          subscriberId,
          frameCount: this.frameCount,
          timestamp: performance.now(),
          cropRegion: this.cropRegion
        });
        
        // Handle async callbacks
        if (result instanceof Promise) {
          promises.push(result);
        }
      } catch (error) {
        this.log(`Error notifying subscriber ${subscriberId}:`, error);
      }
    }

    // Wait for all async callbacks to complete
    if (promises.length > 0) {
      await Promise.allSettled(promises);
    }
  }

  /**
   * Subscribe to processed frames
   * @param {string} subscriberId - Unique identifier for the subscriber
   * @param {Function} callback - Callback function to receive frames
   * @returns {Function} - Unsubscribe function
   */
  subscribe(subscriberId, callback) {
    if (typeof callback !== 'function') {
      throw new Error('Callback must be a function');
    }

    this.subscribers.set(subscriberId, callback);
    this.log(`Subscriber added: ${subscriberId} (total: ${this.subscribers.size})`);

    // Return unsubscribe function
    return () => this.unsubscribe(subscriberId);
  }

  /**
   * Unsubscribe from processed frames
   * @param {string} subscriberId - Subscriber ID to remove
   */
  unsubscribe(subscriberId) {
    const removed = this.subscribers.delete(subscriberId);
    if (removed) {
      this.log(`Subscriber removed: ${subscriberId} (remaining: ${this.subscribers.size})`);
    }
    return removed;
  }

  /**
   * Update crop region
   * @param {Object} cropRegion - New crop region {x, y, width, height}
   */
  setCropRegion(cropRegion) {
    if (cropRegion && typeof cropRegion === 'object') {
      this.cropRegion = {
        x: this.makeEven(cropRegion.x || 0),
        y: this.makeEven(cropRegion.y || 0),
        width: this.makeEven(cropRegion.width || 100),
        height: this.makeEven(cropRegion.height || 100)
      };
      this.log('Crop region updated:', this.cropRegion);
    } else {
      this.cropRegion = null;
      this.log('Crop region cleared');
    }
  }

  /**
   * Enable or disable the interceptor
   * @param {boolean} enabled - Whether to enable the interceptor
   */
  setEnabled(enabled) {
    this.isEnabled = !!enabled;
    this.log(`Interceptor ${this.isEnabled ? 'enabled' : 'disabled'}`);
  }

  /**
   * Enable or disable cropping functionality
   * @param {boolean} enabled - Whether to enable cropping
   */
  setCroppingEnabled(enabled) {
    this.options.enableCropping = !!enabled;
    this.log(`Cropping ${this.options.enableCropping ? 'enabled' : 'disabled'}`);
  }

  /**
   * Get current interceptor status
   * @returns {Object} - Status information
   */
  getStatus() {
    return {
      isEnabled: this.isEnabled,
      croppingEnabled: this.options.enableCropping,
      cropRegion: this.cropRegion,
      subscriberCount: this.subscribers.size,
      frameCount: this.frameCount,
      hasOriginalTrack: !!this.originalTrack,
      hasProcessedTrack: !!this.processedTrack
    };
  }

  /**
   * Clean up resources
   */
  destroy() {
    this.log('Destroying interceptor...');
    
    // Clear subscribers
    this.subscribers.clear();
    
    // Stop tracks
    if (this.originalTrack) {
      this.originalTrack.stop();
    }
    if (this.processedTrack) {
      this.processedTrack.stop();
    }
    
    // Clean up transform stream
    if (this.transformStream) {
      try {
        this.transformStream.writable.abort();
        this.transformStream.readable.cancel();
      } catch (error) {
        this.log('Error cleaning up transform stream:', error);
      }
    }

    this.log('Interceptor destroyed');
  }

  /**
   * Utility function to ensure even numbers for YUV 4:2:0 alignment
   * @param {number} value - Input value
   * @returns {number} - Even value
   */
  makeEven(value) {
    return Math.floor(value / 2) * 2;
  }

  /**
   * Logging utility
   * @param {...any} args - Arguments to log
   */
  log(...args) {
    if (this.options.debug) {
      console.log('[VideoFrameInterceptor]', ...args);
    }
  }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
  module.exports = VideoFrameInterceptor;
} else if (typeof window !== 'undefined') {
  window.VideoFrameInterceptor = VideoFrameInterceptor;
}
