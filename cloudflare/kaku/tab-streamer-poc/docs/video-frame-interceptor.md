# Video Frame Interceptor

A modular video frame interceptor that provides per-client video processing capabilities. Each web client connection has its own independent interceptor instance, enabling customized video processing settings for individual clients viewing the same source stream.

## Features

- **Per-Client Processing**: Each web client has its own interceptor instance with independent settings
- **Frame Cropping**: Apply configurable crop regions to video frames with YUV 4:2:0 alignment
- **Event-driven Subscription API**: Allow multiple components to subscribe to processed frames per client
- **Toggle-able Functionality**: Enable/disable interceptor and cropping independently per client
- **Real-time Processing**: Maintains streaming performance while processing frames individually
- **Multiple Subscribers**: Support for multiple frame listeners with isolated frame clones per client
- **Independent Control**: Each client can have different crop regions and settings simultaneously

## Architecture

The interceptor operates at the individual client peer connection level, with each web client having its own interceptor instance:

```
Target Tab → Raw WebRTC Stream → Control Tab → Per-Client Interceptors → Individual Web Clients
                                                        ↓
                                                Frame Subscribers (per client)
```

### Per-Client Processing Pipeline

```
Incoming Stream → Client 1 Interceptor → MediaStreamTrackProcessor → TransformStream → Output to Client 1
                ↓                                                          ↓
                Client 2 Interceptor → MediaStreamTrackProcessor → TransformStream → Output to Client 2
                ↓                                                          ↓
                Client N Interceptor → MediaStreamTrackProcessor → TransformStream → Output to Client N
                                                                   ↓
                                            Frame Analysis & Cropping (per client)
                                                                   ↓
                                            Subscriber Notifications (per client)
```

## Installation

The interceptor is automatically included when using the tab streaming system. For standalone use:

```html
<script src="injection-scripts/video-frame-interceptor.js"></script>
```

## Per-Client Usage

### Automatic Per-Client Setup

The interceptor system now operates on a per-client basis. Each web client connection automatically gets its own interceptor instance:

```javascript
// In the control tab - interceptors are created automatically per client
class ControlTabManager {
  initializeClientInterceptor(webClientId) {
    const config = this.getClientInterceptorConfig(webClientId);

    const interceptor = new VideoFrameInterceptor({
      debug: true,
      enableCropping: config.croppingEnabled,
      defaultCropRegion: config.cropRegion,
    });

    this.clientInterceptors.set(webClientId, interceptor);
    return interceptor;
  }

  processStreamForClient(stream, webClientId) {
    const interceptor = this.clientInterceptors.get(webClientId);
    if (!interceptor) return stream;

    const videoTrack = stream.getVideoTracks()[0];
    const processedTrack = interceptor.initialize(videoTrack);

    return new MediaStream([processedTrack, ...stream.getAudioTracks()]);
  }
}
```

### Web Client Controls

Each web client can control their own interceptor settings independently:

```javascript
// Toggle cropping for this specific client
function toggleCropping() {
  websocket.send(
    JSON.stringify({
      type: "toggle-interceptor-cropping",
      webClientId: clientId,
      enabled: !currentCroppingState,
    })
  );
}

// Set crop region for this specific client
function setCropRegion(x, y, width, height) {
  websocket.send(
    JSON.stringify({
      type: "set-interceptor-crop-region",
      webClientId: clientId,
      cropRegion: { x, y, width, height },
    })
  );
}
```

### Subscribe to Frames

```javascript
// Subscribe to processed frames
const unsubscribe = interceptor.subscribe("my-analyzer", (frame, metadata) => {
  console.log("Received frame:", {
    timestamp: metadata.timestamp,
    frameCount: metadata.frameCount,
    cropRegion: metadata.cropRegion,
  });

  // Process the frame
  analyzeFrame(frame);

  // Always close the frame when done
  frame.close();
});

// Unsubscribe when done
unsubscribe();
```

## Configuration Options

### Constructor Options

```javascript
const options = {
  // Enable debug logging
  debug: false,

  // Default crop region (optional)
  defaultCropRegion: { x: 0, y: 0, width: 100, height: 100 },

  // Target frame rate (for reference)
  frameRate: 30,

  // Enable cropping functionality
  enableCropping: true,
};

const interceptor = new VideoFrameInterceptor(options);
```

### Crop Region Format

Crop regions must specify x, y, width, and height coordinates:

```javascript
const cropRegion = {
  x: 100, // Left offset (must be even for YUV alignment)
  y: 100, // Top offset (must be even for YUV alignment)
  width: 400, // Crop width (must be even for YUV alignment)
  height: 300, // Crop height (must be even for YUV alignment)
};

interceptor.setCropRegion(cropRegion);
```

**Note**: All values are automatically adjusted to even numbers for YUV 4:2:0 alignment requirements.

## API Reference

### Methods

#### `initialize(videoTrack)`

Initialize the interceptor with a video track.

- **Parameters**: `videoTrack` (MediaStreamTrack) - The original video track
- **Returns**: MediaStreamTrack - The processed video track

#### `subscribe(subscriberId, callback)`

Subscribe to processed frames.

- **Parameters**:
  - `subscriberId` (string) - Unique identifier for the subscriber
  - `callback` (function) - Callback function `(frame, metadata) => void`
- **Returns**: Function - Unsubscribe function

#### `unsubscribe(subscriberId)`

Remove a frame subscriber.

- **Parameters**: `subscriberId` (string) - Subscriber ID to remove
- **Returns**: boolean - True if subscriber was removed

#### `setCropRegion(cropRegion)`

Update the crop region.

- **Parameters**: `cropRegion` (object|null) - Crop region or null to disable

#### `setEnabled(enabled)`

Enable or disable the interceptor.

- **Parameters**: `enabled` (boolean) - Whether to enable the interceptor

#### `setCroppingEnabled(enabled)`

Enable or disable cropping functionality.

- **Parameters**: `enabled` (boolean) - Whether to enable cropping

#### `getStatus()`

Get current interceptor status.

- **Returns**: Object with current status information

#### `destroy()`

Clean up resources and stop processing.

### Events and Callbacks

#### Frame Callback

```javascript
function frameCallback(frame, metadata) {
  // frame: VideoFrame - The processed video frame
  // metadata: Object - Frame metadata

  const {
    subscriberId, // string - Your subscriber ID
    frameCount, // number - Total frames processed
    timestamp, // number - Current timestamp
    cropRegion, // object|null - Current crop region
  } = metadata;

  // Process the frame...

  // IMPORTANT: Always close the frame when done
  frame.close();
}
```

## Integration with Tab Streaming System

The interceptor is automatically integrated into the control tab and processes incoming streams from target tabs. Control it through the control tab interface or programmatically:

### Control Tab Integration

The control tab provides UI controls for:

- Enabling/disabling the interceptor
- Toggling cropping functionality
- Setting crop regions
- Viewing interceptor status

### Programmatic Control

```javascript
// Direct control through control tab manager
const controlTab = window.pocControlTabManager;

// Toggle interceptor
controlTab.toggleInterceptor(tabId);

// Toggle cropping
controlTab.toggleCropping(tabId);

// Set crop region (uses input field value)
controlTab.setCropRegion(tabId);

// Direct interceptor access
if (window.frameInterceptor) {
  window.frameInterceptor.setEnabled(true);
  window.frameInterceptor.setCroppingEnabled(true);
  window.frameInterceptor.setCropRegion({
    x: 100,
    y: 100,
    width: 400,
    height: 300,
  });
}
```

## Performance Considerations

### Frame Processing

- Frames are processed in real-time without blocking the main thread
- Subscribers receive cloned frames to prevent conflicts
- Failed frame processing falls back to passing through original frames

### Memory Management

- Always call `frame.close()` in subscriber callbacks
- The interceptor automatically manages transform stream resources
- Use `destroy()` method for proper cleanup

### Cropping Performance

- Cropping uses VideoFrame constructor with visibleRect for hardware acceleration
- YUV alignment ensures optimal performance
- Invalid crop regions fall back to original frames

## Error Handling

The interceptor includes comprehensive error handling:

```javascript
// Errors in frame processing don't stop the stream
interceptor.subscribe("analyzer", (frame, metadata) => {
  try {
    // Your frame processing code
    processFrame(frame);
  } catch (error) {
    console.error("Frame processing error:", error);
  } finally {
    // Always close the frame
    frame.close();
  }
});
```

## Examples

### Basic Screen Analysis

```javascript
const interceptor = new VideoFrameInterceptor({ debug: true });
const processedTrack = interceptor.initialize(videoTrack);

interceptor.subscribe("screen-analyzer", (frame, metadata) => {
  // Simple brightness analysis
  const canvas = document.createElement("canvas");
  const ctx = canvas.getContext("2d");

  canvas.width = frame.codedWidth;
  canvas.height = frame.codedHeight;
  ctx.drawImage(frame, 0, 0);

  const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
  const brightness = calculateBrightness(imageData.data);

  console.log("Frame brightness:", brightness);
  frame.close();
});
```

### Dynamic Crop Region

```javascript
// Update crop region based on content analysis
interceptor.subscribe("content-analyzer", (frame, metadata) => {
  const detectedRegion = detectImportantRegion(frame);

  if (detectedRegion) {
    interceptor.setCropRegion(detectedRegion);
  }

  frame.close();
});
```

## Browser Compatibility

- Chrome 94+ (MediaStreamTrackProcessor/Generator support)
- Edge 94+
- Firefox: Not supported (missing MediaStreamTrackProcessor)
- Safari: Not supported (missing MediaStreamTrackProcessor)

## Troubleshooting

### Common Issues

1. **"MediaStreamTrackProcessor is not defined"**

   - Ensure you're using a supported browser (Chrome 94+)
   - Check that the page is served over HTTPS

2. **Cropping not working**

   - Verify crop region coordinates are within frame bounds
   - Ensure crop dimensions are even numbers
   - Check that cropping is enabled

3. **Performance issues**
   - Reduce the number of subscribers
   - Increase analysis intervals in subscribers
   - Ensure frame.close() is called in all callbacks

### Debug Mode

Enable debug mode for detailed logging:

```javascript
const interceptor = new VideoFrameInterceptor({ debug: true });
```

This will log:

- Interceptor initialization
- Frame processing statistics
- Subscriber management
- Error conditions
