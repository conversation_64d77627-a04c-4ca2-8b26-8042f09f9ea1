/**
 * Main POC Streaming System Orchestrator
 *
 * Coordinates browser management, script injection, and signaling server
 * Provides the main entry point for the streaming system
 */

import { BrowserManager } from "./browser-manager.js";
import { ScriptInjector } from "./script-injector.js";
import { SignalingServer } from "./signaling-server.js";
import express from "express";
import path from "path";
import { fileURLToPath } from "url";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

export class POCStreamingSystem {
  constructor(options = {}) {
    this.options = {
      browserPort: options.browserPort || 9222,
      signalingPort: options.signalingPort || 8080,
      webServerPort: options.webServerPort || 3000,
      headless: options.headless || false,
      ...options,
    };

    this.browserManager = null;
    this.scriptInjector = null;
    this.signalingServer = null;
    this.webServer = null;
    this.isRunning = false;

    // State management
    this.targetTabs = new Map(); // tabId -> { url, isInjected }
    this.reconnectionAttempts = new Map(); // tabId -> attemptCount
    this.maxReconnectionAttempts = 5;
  }

  /**
   * Initialize and start the complete streaming system
   */
  async start() {
    if (this.isRunning) {
      console.log("POC Streaming System already running");
      return;
    }

    console.log("🚀 Starting POC Browser Streaming System...");

    try {
      // Start signaling server first
      await this.startSignalingServer();

      // Start web server for client UI
      await this.startWebServer();

      // Initialize browser manager
      await this.initializeBrowserManager();

      // Initialize script injector
      await this.initializeScriptInjector();

      // Setup system event handlers
      this.setupEventHandlers();

      this.isRunning = true;

      console.log("✅ POC Streaming System started successfully!");
      console.log(
        `📋 Control interface: http://localhost:${this.options.webServerPort}`
      );
      console.log(
        `🌐 Web client: http://localhost:${this.options.webServerPort}`
      );
      console.log(
        `📡 Signaling server: ws://localhost:${this.options.signalingPort}`
      );
      console.log(
        `🔧 Browser CDP: http://localhost:${this.options.browserPort}`
      );
    } catch (error) {
      console.error("❌ Failed to start POC Streaming System:", error);
      await this.stop();
      throw error;
    }
  }

  /**
   * Start the WebSocket signaling server
   */
  async startSignalingServer() {
    console.log("📡 Starting signaling server...");

    this.signalingServer = new SignalingServer({
      port: this.options.signalingPort,
      scriptInjector: this.scriptInjector,
    });

    await this.signalingServer.start();
  }

  /**
   * Start the web server for client UI
   */
  async startWebServer() {
    console.log("🌐 Starting web server...");

    const app = express();

    // Serve static files from web-client directory
    const webClientPath = path.join(__dirname, "client");
    app.use(express.static(webClientPath));

    // API endpoints
    app.get("/api/status", (req, res) => {
      res.json({
        isRunning: this.isRunning,
        stats: this.getSystemStats(),
      });
    });

    app.get("/api/targets", (req, res) => {
      const targets = Array.from(this.targetTabs.entries()).map(
        ([tabId, info]) => ({
          tabId,
          ...info,
        })
      );
      res.json(targets);
    });

    app.post("/api/targets/:tabId/inject", async (req, res) => {
      try {
        const { tabId } = req.params;
        await this.injectScriptIntoTab(tabId);
        res.json({ success: true, message: "Script injected successfully" });
      } catch (error) {
        res.status(500).json({ success: false, error: error.message });
      }
    });

    app.post("/api/targets/create", async (req, res) => {
      try {
        const { url } = req.body;
        const targetTab = await this.createTargetTab(url);
        res.json({ success: true, targetTab });
      } catch (error) {
        res.status(500).json({ success: false, error: error.message });
      }
    });

    // Start server
    this.webServer = app.listen(this.options.webServerPort, () => {
      console.log(
        `✅ Web server started on port ${this.options.webServerPort}`
      );
    });
  }

  /**
   * Initialize browser manager and create control tab
   */
  async initializeBrowserManager() {
    console.log("🌐 Initializing browser manager...");

    this.browserManager = new BrowserManager({
      port: this.options.browserPort,
      headless: this.options.headless,
    });

    await this.browserManager.launchBrowser();
    await this.browserManager.createControlTab();
  }

  /**
   * Initialize script injector
   */
  async initializeScriptInjector() {
    console.log("💉 Initializing script injector...");

    this.scriptInjector = new ScriptInjector(
      this.browserManager,
      this.signalingServer
    );

    // Inject control tab script
    const signalingServerUrl = `ws://localhost:${this.options.signalingPort}`;
    await this.scriptInjector.injectControlTabScript(signalingServerUrl);
  }

  /**
   * Setup system event handlers for reconnection and state management
   */
  setupEventHandlers() {
    console.log("🔧 Setting up event handlers...");

    // Handle process termination
    process.on("SIGINT", async () => {
      console.log("\n👋 Shutting down POC Streaming System...");
      await this.stop();
      process.exit(0);
    });

    process.on("SIGTERM", async () => {
      console.log("\n👋 Shutting down POC Streaming System...");
      await this.stop();
      process.exit(0);
    });

    // Handle uncaught exceptions
    process.on("uncaughtException", async (error) => {
      console.error("💥 Uncaught exception:", error);
      await this.stop();
      process.exit(1);
    });
  }

  /**
   * Create a new target tab and inject script
   */
  async createTargetTab(url) {
    console.log(`📑 Creating target tab for: ${url}`);

    const targetTab = await this.browserManager.createTargetTab(url);

    // Track the tab
    this.targetTabs.set(targetTab.id, {
      url,
      isInjected: false,
      createdAt: Date.now(),
    });

    // Inject script after a short delay to ensure page loads
    setTimeout(async () => {
      try {
        await this.injectScriptIntoTab(targetTab.id);
      } catch (error) {
        console.error(
          `Failed to inject script into tab ${targetTab.id}:`,
          error
        );
      }
    }, 2000);

    return targetTab;
  }

  /**
   * Inject script into a specific target tab
   */
  async injectScriptIntoTab(tabId) {
    const signalingServerUrl = `ws://localhost:${this.options.signalingPort}`;

    try {
      await this.scriptInjector.injectScript(tabId, signalingServerUrl);

      // Update tracking
      const tabInfo = this.targetTabs.get(tabId);
      if (tabInfo) {
        tabInfo.isInjected = true;
        tabInfo.lastInjected = Date.now();
      }

      // Reset reconnection attempts
      this.reconnectionAttempts.delete(tabId);

      console.log(`✅ Script injected into tab: ${tabId}`);
    } catch (error) {
      console.error(`❌ Failed to inject script into tab ${tabId}:`, error);

      // Handle reconnection logic
      await this.handleInjectionFailure(tabId, error);
      throw error;
    }
  }

  /**
   * Handle script injection failures with reconnection logic
   */
  async handleInjectionFailure(tabId, error) {
    const attempts = this.reconnectionAttempts.get(tabId) || 0;

    if (attempts >= this.maxReconnectionAttempts) {
      console.error(`❌ Max reconnection attempts reached for tab: ${tabId}`);
      return;
    }

    console.log(
      `🔄 Retrying script injection for tab ${tabId} (attempt ${attempts + 1})`
    );

    this.reconnectionAttempts.set(tabId, attempts + 1);

    // Exponential backoff
    const delay = Math.pow(2, attempts) * 1000;

    setTimeout(async () => {
      try {
        await this.injectScriptIntoTab(tabId);
      } catch (retryError) {
        console.error(`Retry failed for tab ${tabId}:`, retryError);
      }
    }, delay);
  }

  /**
   * Monitor target tabs for navigation and reload events
   */
  async monitorTargetTabs() {
    console.log("👀 Starting target tab monitoring...");

    setInterval(async () => {
      try {
        const targets = await this.browserManager.getTargets();

        for (const target of targets) {
          if (target.type === "page" && this.targetTabs.has(target.id)) {
            const tabInfo = this.targetTabs.get(target.id);

            // Check if URL changed (navigation)
            if (target.url !== tabInfo.url) {
              console.log(`🔄 Tab ${target.id} navigated to: ${target.url}`);

              tabInfo.url = target.url;
              tabInfo.isInjected = false;

              // Re-inject script after navigation
              setTimeout(async () => {
                try {
                  await this.injectScriptIntoTab(target.id);
                } catch (error) {
                  console.error(
                    `Failed to re-inject script after navigation:`,
                    error
                  );
                }
              }, 1000);
            }
          }
        }
      } catch (error) {
        console.error("Error monitoring target tabs:", error);
      }
    }, 5000); // Check every 5 seconds
  }

  /**
   * Get system statistics
   */
  getSystemStats() {
    return {
      isRunning: this.isRunning,
      targetTabs: this.targetTabs.size,
      injectedTabs: Array.from(this.targetTabs.values()).filter(
        (tab) => tab.isInjected
      ).length,
      signalingStats: this.signalingServer
        ? this.signalingServer.getStats()
        : null,
      uptime: this.isRunning ? Date.now() - this.startTime : 0,
    };
  }

  /**
   * Stop the complete streaming system
   */
  async stop() {
    if (!this.isRunning) return;

    console.log("🛑 Stopping POC Streaming System...");

    try {
      // Stop monitoring
      if (this.monitoringInterval) {
        clearInterval(this.monitoringInterval);
      }

      // Cleanup script injector
      if (this.scriptInjector) {
        await this.scriptInjector.cleanup();
      }

      // Cleanup browser manager
      if (this.browserManager) {
        await this.browserManager.cleanup();
      }

      // Stop signaling server
      if (this.signalingServer) {
        await this.signalingServer.stop();
      }

      // Stop web server
      if (this.webServer) {
        this.webServer.close();
      }

      this.isRunning = false;
      console.log("✅ POC Streaming System stopped");
    } catch (error) {
      console.error("❌ Error during shutdown:", error);
    }
  }
}

// CLI interface
if (import.meta.url === `file://${process.argv[1]}`) {
  const system = new POCStreamingSystem();

  // Parse command line arguments
  const args = process.argv.slice(2);
  const options = {};

  for (let i = 0; i < args.length; i += 2) {
    const key = args[i].replace("--", "");
    const value = args[i + 1];

    if (key === "headless") {
      options.headless = value === "true";
    } else if (key === "browser-port") {
      options.browserPort = parseInt(value);
    } else if (key === "signaling-port") {
      options.signalingPort = parseInt(value);
    } else if (key === "web-port") {
      options.webServerPort = parseInt(value);
    }
  }

  // Apply options
  Object.assign(system.options, options);

  // Start the system
  system.start().catch((error) => {
    console.error("Failed to start system:", error);
    process.exit(1);
  });
}
