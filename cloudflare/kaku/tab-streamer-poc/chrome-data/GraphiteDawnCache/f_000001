SE      
                    #ifdef __clang__
                    #pragma clang diagnostic ignored "-Wall"
                    #endif
                #include <metal_stdlib>
using namespace metal;

struct tint_struct {
  uint tint_member;
  float4 tint_member_1;
  float4 tint_member_2;
  float2 tint_member_3;
  float2 tint_member_4;
  float tint_member_5;
  float tint_member_6;
  uint2 tint_member_7;
};

struct tint_struct_1 {
  float4 tint_member_8;
  float4 tint_member_9;
};

struct tint_struct_3 {
  /* 0x0000 */ float4 tint_member_11;
  /* 0x0010 */ float4 tint_member_12;
};

template<typename T, size_t N>
struct tint_array {
  const constant T& operator[](size_t i) const constant { return elements[i]; }
  device T& operator[](size_t i) device { return elements[i]; }
  const device T& operator[](size_t i) const device { return elements[i]; }
  thread T& operator[](size_t i) thread { return elements[i]; }
  const thread T& operator[](size_t i) const thread { return elements[i]; }
  threadgroup T& operator[](size_t i) threadgroup { return elements[i]; }
  const threadgroup T& operator[](size_t i) const threadgroup { return elements[i]; }
  T elements[N];
};

struct tint_struct_5 {
  /* 0x0000 */ float4 tint_member_15;
  /* 0x0010 */ float2 tint_member_16;
  /* 0x0018 */ float tint_member_17;
  /* 0x001c */ tint_array<int8_t, 4> tint_pad;
};

struct tint_struct_4 {
  /* 0x0000 */ tint_array<tint_struct_5, 1> tint_member_14;
};

struct tint_struct_7 {
  /* 0x0000 */ float4 tint_member_20;
};

struct tint_struct_6 {
  /* 0x0000 */ tint_array<tint_struct_7, 1> tint_member_19;
};

struct tint_struct_2 {
  const constant tint_struct_3* tint_member_10;
  const device tint_struct_4* tint_member_13;
  const device tint_struct_6* tint_member_18;
};

struct tint_struct_8 {
  float4 tint_member_21 [[position]];
  float4 tint_member_22 [[user(locn0)]] [[center_no_perspective]];
};

struct tint_struct_9 {
  float4 tint_member_23 [[attribute(0)]];
  float4 tint_member_24 [[attribute(1)]];
  float2 tint_member_25 [[attribute(2)]];
  float2 tint_member_26 [[attribute(3)]];
  float tint_member_27 [[attribute(4)]];
  float tint_member_28 [[attribute(5)]];
  uint2 tint_member_29 [[attribute(6)]];
};

float2x2 v(float2x2 v_1) {
  float2 const v_2 = float2(v_1[1].y, -(v_1[0].y));
  return (float2x2(v_2, float2(-(v_1[1].x), v_1[0].x)) * (1.0f / determinant(v_1)));
}

float v_3(float2 v_4, float2 v_5, float2 v_6, float2 v_7, float2x2 v_8) {
  float2 const v_9 = fma(float2(-2.0f), v_5, v_6);
  float2 const v_10 = (v_8 * (v_9 + v_4));
  float2 const v_11 = fma(float2(-2.0f), v_6, v_7);
  float2 const v_12 = (v_8 * (v_11 + v_5));
  float const v_13 = dot(v_10, v_10);
  float const v_14 = dot(v_12, v_12);
  float const v_15 = max(v_13, v_14);
  return v_15;
}

float v_16(float2 v_17, float2 v_18, float2 v_19, float v_20) {
  float2 v_21 = v_17;
  float2 v_22 = v_18;
  float2 v_23 = v_19;
  float2 const v_24 = min(v_21, v_22);
  float2 const v_25 = min(v_24, v_23);
  float2 const v_26 = max(v_21, v_22);
  float2 const v_27 = max(v_26, v_23);
  float2 const v_28 = ((v_25 + v_27) * 0.5f);
  v_21 = (v_21 - v_28);
  v_22 = (v_22 - v_28);
  v_23 = (v_23 - v_28);
  float const v_29 = dot(v_21, v_21);
  float const v_30 = dot(v_22, v_22);
  float const v_31 = max(v_29, v_30);
  float const v_32 = dot(v_23, v_23);
  float const v_33 = max(v_31, v_32);
  float const v_34 = sqrt(v_33);
  float const v_35 = v_34;
  float2 const v_36 = float2((-2.0f * v_20));
  float2 const v_37 = fma(v_36, v_22, v_21);
  float2 const v_38 = (v_37 + v_23);
  float const v_39 = fma(-2.0f, v_20, 2.0f);
  float const v_40 = abs(v_39);
  float const v_41 = v_40;
  float const v_42 = fma(v_35, 4.0f, -1.0f);
  float const v_43 = max(0.0f, v_42);
  float const v_44 = v_43;
  float const v_45 = length(v_38);
  float const v_46 = ((v_45 * 4.0f) + (v_44 * v_41));
  float const v_47 = min(v_20, 1.0f);
  float const v_48 = (4.0f * v_47);
  return (v_46 / v_48);
}

float2 v_49(float2 v_50, float2 v_51) {
  float2 const v_52 = (v_50 - v_51);
  if (all((v_52 == float2(0.0f)))) {
    return float2(0.0f);
  } else {
    float const v_53 = abs(v_52.x);
    float const v_54 = abs(v_52.y);
    float const v_55 = max(v_53, v_54);
    float const v_56 = (1.0f / v_55);
    float2 const v_57 = normalize((v_56 * v_52));
    return v_57;
  }
  /* unreachable */
  return 0.0f;
}

float2 v_58(float2 v_59, float2 v_60, float v_61) {
  float2 const v_62 = fma((v_60 - v_59), float2(v_61), v_59);
  return v_62;
}

void v_63(tint_struct v_64, thread tint_struct_1* const v_65, tint_struct_2 v_66) {
  uint const v_67 = v_64.tint_member_7.x;
  float4 const v_68 = (*v_66.tint_member_13).tint_member_14[v_67].tint_member_15;
  float2 const v_69 = (*v_66.tint_member_13).tint_member_14[v_67].tint_member_16;
  float const v_70 = (*v_66.tint_member_13).tint_member_14[v_67].tint_member_17;
  float v_71 = float((int(v_64.tint_member) >> (1u & 31u)));
  if (((int(v_64.tint_member) & 1) != 0)) {
    v_71 = -(v_71);
  }
  float2 const v_72 = v_68.xy;
  float2 const v_73 = v_68.zw;
  float2 const v_74 = float2(v_72.x, v_72.y);
  float2x2 const v_75 = float2x2(v_74, float2(v_73.x, v_73.y));
  float4 v_76 = 0.0f;
  float2 v_77 = v_64.tint_member_3;
  float2 v_78 = v_64.tint_member_1.xy;
  float2 v_79 = v_64.tint_member_1.zw;
  float2 v_80 = v_64.tint_member_2.xy;
  float2 v_81 = v_64.tint_member_2.zw;
  float v_82 = -1.0f;
  if ((v_64.tint_member_6 != 0.0f)) {
    v_82 = v_81.x;
    v_81 = v_80;
  }
  float v_83 = 0.0f;
  if ((v_82 < 0.0f)) {
    bool v_84 = false;
    if (all((v_78 == v_79))) {
      v_84 = all((v_80 == v_81));
    } else {
      v_84 = false;
    }
    if (v_84) {
      v_83 = 1.0f;
    } else {
      float const v_85 = v_3(v_78, v_79, v_80, v_81, v_75);
      float const v_86 = v_85;
      float const v_87 = sqrt(v_86);
      float const v_88 = sqrt((3.0f * v_87));
      float const v_89 = ceil(v_88);
      float const v_90 = max(v_89, 1.0f);
      v_83 = v_90;
    }
  } else {
    float const v_91 = v_16((v_75 * v_78), (v_75 * v_79), (v_75 * v_80), v_82);
    float const v_92 = v_91;
    float const v_93 = sqrt(v_92);
    float const v_94 = ceil(v_93);
    float const v_95 = max(v_94, 1.0f);
    v_83 = v_95;
  }
  float v_96 = v_64.tint_member_4.x;
  float const v_97 = v_64.tint_member_4.y;
  bool const v_98 = (v_64.tint_member_4.x == 0.0f);
  float v_99 = 0.0f;
  if (v_98) {
    v_99 = 0.47746482491493225098f;
    v_96 = 0.5f;
  } else {
    float const v_100 = max((1.0f - (0.25f / (v_70 * v_64.tint_member_4.x))), -1.0f);
    float const v_101 = acos(v_100);
    v_99 = (0.5f / v_101);
  }
  if (v_98) {
    v_78 = (v_75 * v_78);
    v_79 = (v_75 * v_79);
    v_80 = (v_75 * v_80);
    v_81 = (v_75 * v_81);
    v_77 = (v_75 * v_77);
  }
  float2 v_102 = 0.0f;
  if (all((v_78 == v_79))) {
    float2 const v_103 = v_80;
    float2 const v_104 = v_81;
    v_102 = select(v_103, v_104, bool2(all((v_79 == v_80))));
  } else {
    v_102 = v_79;
  }
  float2 const v_105 = v_49(v_102, v_78);
  float2 v_106 = v_105;
  float2 v_107 = 0.0f;
  if (all((v_81 == v_80))) {
    float2 const v_108 = v_79;
    float2 const v_109 = v_78;
    v_107 = select(v_108, v_109, bool2(all((v_80 == v_79))));
  } else {
    v_107 = v_80;
  }
  float2 const v_110 = v_49(v_81, v_107);
  float2 v_111 = v_110;
  if (all((v_106 == float2(0.0f)))) {
    v_106 = float2(1.0f, 0.0f);
    v_111 = float2(-1.0f, 0.0f);
  }
  float v_112 = 0.0f;
  if ((v_97 >= 0.0f)) {
    float const v_113 = sign(v_97);
    v_112 = (v_113 + 3.0f);
  } else {
    float2 const v_114 = v_49(v_78, v_77);
    float2 const v_115 = v_114;
    float const v_116 = dot(v_115, v_106);
    float const v_117 = clamp(v_116, -1.0f, 1.0f);
    float const v_118 = acos(v_117);
    float const v_119 = v_118;
    float const v_120 = ceil((v_119 * v_99));
    float const v_121 = max(v_120, 1.0f);
    float const v_122 = v_121;
    v_112 = (v_122 + 2.0f);
    float const v_123 = min(v_112, 16381.0f);
    v_112 = v_123;
  }
  float2 const v_124 = (v_80 - v_78);
  float2 const v_125 = (v_81 - v_79);
  float2 const v_126 = float2(v_124.x, v_124.y);
  float const v_127 = determinant(float2x2(v_126, float2(v_125.x, v_125.y)));
  float v_128 = v_127;
  float const v_129 = abs(v_71);
  float v_130 = (v_129 - v_112);
  if ((v_130 < 0.0f)) {
    v_111 = v_106;
    if (any((v_77 != v_78))) {
      float2 const v_131 = v_49(v_78, v_77);
      v_106 = v_131;
    }
    float const v_132 = v_111.x;
    float const v_133 = v_111.y;
    float2 const v_134 = float2(v_106.x, v_106.y);
    float const v_135 = determinant(float2x2(v_134, float2(v_132, v_133)));
    v_128 = v_135;
  }
  float const v_136 = dot(v_106, v_111);
  float const v_137 = clamp(v_136, -1.0f, 1.0f);
  float const v_138 = v_137;
  float const v_139 = acos(v_138);
  float v_140 = v_139;
  if ((v_128 < 0.0f)) {
    v_140 = -(v_140);
  }
  float v_141 = 0.0f;
  float const v_142 = sign(v_71);
  float v_143 = v_142;
  if ((v_130 < 0.0f)) {
    v_141 = (v_112 - 2.0f);
    v_83 = 1.0f;
    v_79 = v_78;
    v_80 = v_79;
    v_81 = v_80;
    v_130 = (v_130 + (v_141 + 1.0f));
    if ((v_130 < 0.0f)) {
      v_130 = 0.0f;
    } else {
      float const v_144 = abs(v_128);
      float const v_145 = dot(v_106, v_106);
      float const v_146 = dot(v_111, v_111);
      float const v_147 = rsqrt((v_145 * v_146));
      bool const v_148 = ((v_144 * v_147) < 0.00999999977648258209f);
      float const v_149 = dot(v_106, v_111);
      bool v_150 = false;
      if (!(v_148)) {
        v_150 = true;
      } else {
        v_150 = (v_149 < 0.0f);
      }
      if (v_150) {
        float v_151 = 0.0f;
        if ((v_128 < 0.0f)) {
          float const v_152 = min(v_143, 0.0f);
          v_151 = v_152;
        } else {
          float const v_153 = max(v_143, 0.0f);
          v_151 = v_153;
        }
        v_143 = v_151;
      }
    }
  } else {
    float const v_154 = ((16383.0f - v_112) - 1.0f);
    float const v_155 = abs(v_140);
    float const v_156 = ceil((v_155 * v_99));
    float const v_157 = max(v_156, 1.0f);
    v_141 = v_157;
    float const v_158 = min(v_141, v_154);
    v_141 = v_158;
    float const v_159 = min(v_83, ((v_154 - v_141) + 1.0f));
    v_83 = v_159;
  }
  float const v_160 = (v_140 / v_141);
  float const v_161 = ((v_83 + v_141) - 1.0f);
  bool const v_162 = (v_130 >= v_161);
  if ((v_130 > v_161)) {
    v_143 = 0.0f;
  }
  float const v_163 = abs(v_71);
  bool v_164 = false;
  if ((v_163 == 2.0f)) {
    v_164 = (v_97 > 0.0f);
  } else {
    v_164 = false;
  }
  if (v_164) {
    float const v_165 = fma(v_138, 0.5f, 0.5f);
    float const v_166 = v_165;
    float v_167 = 0.0f;
    if ((((v_166 * v_97) * v_97) >= 1.0f)) {
      float const v_168 = rsqrt(v_166);
      v_167 = v_168;
    } else {
      float const v_169 = sqrt(v_166);
      v_167 = v_169;
    }
    v_143 = (v_143 * v_167);
  }
  float2 v_170 = 0.0f;
  float2 v_171 = 0.0f;
  bool v_172 = false;
  if ((v_130 != 0.0f)) {
    v_172 = !(v_162);
  } else {
    v_172 = false;
  }
  if (v_172) {
    float2 v_173 = 0.0f;
    float2 v_174 = 0.0f;
    float2 v_175 = (v_79 - v_78);
    float2 const v_176 = (v_81 - v_78);
    if ((v_82 >= 0.0f)) {
      v_175 = (v_175 * v_82);
      v_174 = ((0.5f * v_176) - v_175);
      v_173 = ((v_82 - 1.0f) * v_176);
      v_79 = (v_79 * v_82);
    } else {
      float2 const v_177 = (v_80 - v_79);
      v_174 = (v_177 - v_175);
      float2 const v_178 = fma(float2(-3.0f), v_177, v_176);
      v_173 = v_178;
    }
    float2 const v_179 = (v_174 * (v_83 * 2.0f));
    float2 const v_180 = (v_175 * (v_83 * v_83));
    float v_181 = 0.0f;
    float const v_182 = min((v_83 - 1.0f), v_130);
    float const v_183 = v_182;
    float const v_184 = abs(v_160);
    float const v_185 = -(v_184);
    float const v_186 = abs(v_160);
    float const v_187 = ((1.0f + v_130) * v_186);
    float v_188 = 32.0f;
    {
      while(true) {
        if ((v_188 >= 1.0f)) {
          float const v_189 = (v_181 + v_188);
          if ((v_189 <= v_183)) {
            float2 const v_190 = float2(v_189);
            float2 const v_191 = fma(v_190, v_173, v_179);
            float2 v_192 = v_191;
            float2 const v_193 = float2(v_189);
            float2 const v_194 = fma(v_193, v_192, v_180);
            v_192 = v_194;
            float2 const v_195 = normalize(v_192);
            float const v_196 = dot(v_195, v_106);
            float const v_197 = v_196;
            float const v_198 = fma(v_189, v_185, v_187);
            float v_199 = v_198;
            float const v_200 = min(v_199, 3.14159274101257324219f);
            v_199 = v_200;
            float const v_201 = cos(v_199);
            if ((v_197 >= v_201)) {
              v_181 = v_189;
            }
          }
        } else {
          break;
        }
        {
          v_188 = (v_188 * 0.5f);
        }
        continue;
      }
    }
    float const v_202 = (v_181 / v_83);
    float const v_203 = (v_130 - v_181);
    float const v_204 = clamp(v_106.x, -1.0f, 1.0f);
    float const v_205 = acos(v_204);
    float v_206 = v_205;
    v_206 = select(-(v_206), v_206, (v_106.y >= 0.0f));
    float const v_207 = fma(v_203, v_160, v_206);
    float const v_208 = v_207;
    float const v_209 = cos(v_208);
    float const v_210 = sin(v_208);
    v_170 = float2(v_209, v_210);
    float2 const v_211 = float2(-(v_170.y), v_170.x);
    float const v_212 = dot(v_211, v_173);
    float const v_213 = v_212;
    float const v_214 = dot(v_211, v_174);
    float const v_215 = v_214;
    float const v_216 = dot(v_211, v_175);
    float const v_217 = v_216;
    float const v_218 = max(((v_215 * v_215) - (v_213 * v_217)), 0.0f);
    float const v_219 = v_218;
    float const v_220 = sqrt(v_219);
    float v_221 = v_220;
    if ((v_215 > 0.0f)) {
      v_221 = -(v_221);
    }
    v_221 = (v_221 - v_215);
    float const v_222 = ((-0.5f * v_221) * v_213);
    float2 v_223 = 0.0f;
    float const v_224 = fma(v_221, v_221, v_222);
    float const v_225 = abs(v_224);
    float const v_226 = fma(v_213, v_217, v_222);
    float const v_227 = abs(v_226);
    if ((v_225 < v_227)) {
      v_223 = float2(v_221, v_213);
    } else {
      v_223 = float2(v_217, v_221);
    }
    float2 const v_228 = v_223;
    float v_229 = 0.0f;
    bool v_230 = false;
    if ((v_203 != 0.0f)) {
      v_230 = (v_228.y != 0.0f);
    } else {
      v_230 = false;
    }
    if (v_230) {
      float const v_231 = saturate((v_228.x / v_228.y));
      v_229 = v_231;
    } else {
      v_229 = 0.0f;
    }
    float const v_232 = v_229;
    float const v_233 = max(v_202, v_232);
    float const v_234 = v_233;
    float2 const v_235 = v_58(v_78, v_79, v_234);
    float2 const v_236 = v_235;
    float2 const v_237 = v_58(v_79, v_80, v_234);
    float2 const v_238 = v_237;
    float2 const v_239 = v_58(v_80, v_81, v_234);
    float2 const v_240 = v_239;
    float2 const v_241 = v_58(v_236, v_238, v_234);
    float2 const v_242 = v_241;
    float2 const v_243 = v_58(v_238, v_240, v_234);
    float2 const v_244 = v_243;
    float2 const v_245 = v_58(v_242, v_244, v_234);
    float2 const v_246 = v_245;
    float const v_247 = fma((v_82 - 1.0f), v_234, 1.0f);
    float const v_248 = v_247;
    float const v_249 = ((v_82 + 1.0f) - v_248);
    float const v_250 = fma((v_249 - v_248), v_234, v_248);
    float const v_251 = v_250;
    if ((v_234 != v_232)) {
      float2 v_252 = 0.0f;
      if ((v_82 >= 0.0f)) {
        float2 const v_253 = v_49((v_238 * v_248), (v_236 * v_249));
        v_252 = v_253;
      } else {
        float2 const v_254 = v_49(v_244, v_242);
        v_252 = v_254;
      }
      v_170 = v_252;
    }
    float2 v_255 = 0.0f;
    if ((v_82 >= 0.0f)) {
      v_255 = (v_242 / v_251);
    } else {
      v_255 = v_246;
    }
    v_171 = v_255;
  } else {
    float2 const v_256 = v_111;
    float2 const v_257 = v_106;
    v_170 = select(v_256, v_257, bool2((v_130 == 0.0f)));
    float2 const v_258 = v_81;
    float2 const v_259 = v_78;
    v_171 = select(v_258, v_259, bool2((v_130 == 0.0f)));
  }
  float2 const v_260 = float2(v_170.y, -(v_170.x));
  v_171 = (v_171 + (v_260 * (v_96 * v_143)));
  if (v_98) {
    float2x2 const v_261 = v(v_75);
    v_76 = float4((v_171 + v_69), (v_261 * v_171));
  } else {
    v_76 = float4(((v_75 * v_171) + v_69), v_171);
  }
  float4 const v_262 = v_76;
  float4 const v_263 = float4(v_262.xy, v_64.tint_member_5, 1.0f);
  float2 const v_264 = sign((*v_66.tint_member_10).tint_member_11.zw);
  (*v_65).tint_member_8 = float4((((*v_66.tint_member_10).tint_member_11.zw * v_263.xy) - (v_264 * v_263.ww)), v_263.zw);
  uint const v_265 = v_64.tint_member_7.y;
  (*v_65).tint_member_9 = float4((*v_66.tint_member_18).tint_member_19[v_265].tint_member_20);
}

tint_struct_1 v_266(tint_struct v_267, tint_struct_2 v_268) {
  tint_struct_1 v_269 = {};
  v_63(v_267, (&v_269), v_268);
  return v_269;
}

vertex tint_struct_8 dawn_entry_point(uint v_271 [[vertex_id]], tint_struct_9 v_272 [[stage_in]], const constant tint_struct_3* v_273 [[buffer(0)]], const device tint_struct_4* v_274 [[buffer(1)]], const device tint_struct_6* v_275 [[buffer(2)]]) {
  tint_struct_2 const v_276 = tint_struct_2{.tint_member_10=v_273, .tint_member_13=v_274, .tint_member_18=v_275};
  tint_struct_1 const v_277 = v_266(tint_struct{.tint_member=v_271, .tint_member_1=v_272.tint_member_23, .tint_member_2=v_272.tint_member_24, .tint_member_3=v_272.tint_member_25, .tint_member_4=v_272.tint_member_26, .tint_member_5=v_272.tint_member_27, .tint_member_6=v_272.tint_member_28, .tint_member_7=v_272.tint_member_29}, v_276);
  tint_struct_8 v_278 = {};
  v_278.tint_member_21 = v_277.tint_member_8;
  v_278.tint_member_22 = v_277.tint_member_9;
  return v_278;
}
       dawn_entry_point                      